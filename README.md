# Hotel Management System

A comprehensive hotel management system built with Java Swing and Derby database.

## Features

### 🏨 Hotel Management Dashboard
- Real-time room occupancy statistics
- Revenue tracking
- Guest management
- Quick access to common hotel operations
- Professional user interface with modern design

### 👥 Guest Registration System
- Register new hotel guests
- Store guest information in Derby database
- View all registered guests
- Input validation and error handling
- Integration with main hotel system

### 🗄️ Database Integration
- Apache Derby embedded database
- Automatic table creation
- Sample data initialization
- CRUD operations for guests and rooms
- Database connection management

## Database Schema

### Tables Created Automatically:
1. **Users** - Registered guests
   - id, username, firstName, lastName, icNumber, registrationDate

2. **Guests** - Hotel guest details
   - id, firstName, lastName, email, phone, address, idNumber, checkInDate, checkOutDate, roomNumber, status

3. **Rooms** - Hotel room information
   - roomNumber, roomType, price, status, description

4. **Bookings** - Room bookings
   - id, guestId, roomNumber, checkInDate, checkOutDate, totalAmount, status, bookingDate

## How to Run

### Option 1: Run Main Application
```bash
java test1.Test1
```
This will show a selection dialog with options:
- Hotel Management System
- Guest Registration
- Database Test
- Exit

### Option 2: Run Specific Components
```bash
# Launch Hotel Management System directly
java test1.Test1 hotel

# Launch Guest Registration directly
java test1.Test1 register

# Run Database Test
java test1.Test1 test
```

### Option 3: Run Individual Classes
```bash
# Hotel Management System
java test1.NewJFrame

# Guest Registration
java test1.RegisterPage

# Database Test
java test1.DatabaseTest
```

## System Requirements

- Java 8 or higher
- Apache Derby database (included in NetBeans)
- Swing GUI library (included in Java)

## Project Structure

```
src/test1/
├── Test1.java              # Main launcher with selection dialog
├── NewJFrame.java          # Hotel Management System main interface
├── RegisterPage.java       # Guest registration interface
├── DatabaseManager.java    # Database connection and operations
├── DatabaseTest.java       # Database connectivity test
├── HelloWorldPage.java     # Simple demo page
└── Other files...          # Additional utility classes
```

## Database Features

### Automatic Setup
- Database is created automatically on first run
- Tables are created with proper relationships
- Sample room data is inserted automatically

### Sample Data
The system includes 8 sample rooms:
- Single rooms (101, 102) - $80/night
- Double rooms (201, 202) - $120/night
- Suites (301, 302) - $250/night
- Deluxe rooms (401, 402) - $180/night

### Room Status Types
- **AVAILABLE** - Ready for booking
- **OCCUPIED** - Currently occupied by guests
- **MAINTENANCE** - Under maintenance

## Usage Instructions

### Guest Registration
1. Launch the Guest Registration system
2. Fill in all required fields:
   - Username (unique)
   - First Name
   - Last Name
   - IC Number (unique, minimum 8 characters)
3. Click "Register Guest" to save to database
4. Use "View All Guests" to see registered guests
5. Use "Back to Hotel System" to return to main dashboard

### Hotel Management Dashboard
1. View real-time statistics from database
2. Use navigation buttons for different hotel functions
3. Quick access buttons for common operations
4. Click "Guest Registration" for new guest registration

### Database Testing
1. Run the database test to verify connectivity
2. Check room statistics
3. Test user registration functionality
4. View sample data

## Technical Details

### Database Connection
- Uses Apache Derby embedded database
- Database file: `HotelDB` (created in project directory)
- Connection URL: `******************************`
- Automatic connection management with singleton pattern

### Error Handling
- Input validation for all forms
- Database error handling with user-friendly messages
- Duplicate username/IC number prevention
- Connection retry logic

### UI Design
- Professional hotel industry color scheme
- Responsive layout with proper spacing
- Hover effects on buttons
- Real-time date/time display
- Tabular data display for guest lists

## Future Enhancements

Potential features to add:
- Room booking system
- Check-in/check-out functionality
- Payment processing
- Reporting and analytics
- Staff management
- Housekeeping management
- Email notifications
- Backup and restore functionality

## Troubleshooting

### Common Issues:
1. **Database connection errors**: Ensure Derby is properly configured in NetBeans
2. **Class not found errors**: Check that all Java files are compiled
3. **GUI not displaying**: Verify Swing is available in your Java installation

### Database Location:
The Derby database files are created in the project root directory as `HotelDB/`.

## Author
Created by kangy for hotel management system demonstration.
