/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package assignment;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class Employee implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String employeeId;          // Unique system-generated ID
    private String firstName;
    private String lastName;
    private String icOrPassportNo;
    private String contactNumber;
    private String email;
    private String password;            // For login
    private String homeAddress;
    private String position;
    private String department;
    private double basicSalary;
    private String bankAccountNo;
    private String epfNumber;
    private String socsoOrTaxId;

    // --- Constructors ---
    public Employee() {}

    public Employee(String employeeId, String firstName, String lastName, String icOrPassportNo,
                    String contactNumber, String email, String password, String homeAddress,
                    String position, String department, double basicSalary, String bankAccountNo,
                    String epfNumber, String socsoOrTaxId) {
        this.employeeId = employeeId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.icOrPassportNo = icOrPassportNo;
        this.contactNumber = contactNumber;
        this.email = email;
        this.password = password;
        this.homeAddress = homeAddress;
        this.position = position;
        this.department = department;
        this.basicSalary = basicSalary;
        this.bankAccountNo = bankAccountNo;
        this.epfNumber = epfNumber;
        this.socsoOrTaxId = socsoOrTaxId;
    }

    // --- Getters & Setters ---

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getIcOrPassportNo() {
        return icOrPassportNo;
    }

    public void setIcOrPassportNo(String icOrPassportNo) {
        this.icOrPassportNo = icOrPassportNo;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public double getBasicSalary() {
        return basicSalary;
    }

    public void setBasicSalary(double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getEpfNumber() {
        return epfNumber;
    }

    public void setEpfNumber(String epfNumber) {
        this.epfNumber = epfNumber;
    }

    public String getSocsoOrTaxId() {
        return socsoOrTaxId;
    }

    public void setSocsoOrTaxId(String socsoOrTaxId) {
        this.socsoOrTaxId = socsoOrTaxId;
    }

    // --- toString() (optional, useful for debugging) ---
    @Override
    public String toString() {
        return "Employee [" +
               "ID='" + employeeId + '\'' +
               ", Name='" + firstName + " " + lastName + '\'' +
               ", Email='" + email + '\'' +
               ", Position='" + position + '\'' +
               ", Department='" + department + '\'' +
               ']';
    }
}
