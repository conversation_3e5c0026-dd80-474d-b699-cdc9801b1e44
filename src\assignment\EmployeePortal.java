/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package assignment;

import java.sql.*;
import javax.swing.*;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class EmployeePortal extends javax.swing.JFrame {
    
    private String employeeID;

    /**
     * Creates new form EmployeePortal
     */
    public EmployeePortal(String employeeID) {
    initComponents();
    if (employeeID != null) {
        this.employeeID = employeeID;
        lblWelcome.setText("Welcome, " + employeeID);
        loadEmployeeDetails();
    } else {
        this.employeeID = "";
        lblWelcome.setText("Welcome!");
    }
}
    
    private void setFieldsEditable(boolean editable) {
        txtFName.setEditable(editable);
        txtLName.setEditable(editable);
        txtICNum.setEditable(editable);
        txtContactNum.setEditable(editable);
        txtEmail.setEditable(editable);
        txtHouseAdd.setEditable(editable);
        txtBankAcc.setEditable(editable);
        txtEPF.setEditable(editable);
        txtTax.setEditable(editable);
        
        txtPassword.setEditable(false);
        
        
        dropListDepartment.setEnabled(false);
        dropListPosition.setEnabled(false);
        txtBasicSalary.setEditable(false);
    }
    
    private void loadEmployeeDetails() {
        try {
            Class.forName("org.apache.derby.jdbc.ClientDriver");
            Connection conn = DriverManager.getConnection("**************************************", "app", "app");
            
            String sql = "SELECT * FROM employee WHERE Employee_ID = ?";
            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, employeeID);
            
            ResultSet rs = stmt.executeQuery();


            if (rs.next()) {
                txtFName.setText(rs.getString("First_Name"));
                txtLName.setText(rs.getString("Last_Name")); 
                txtICNum.setText(rs.getString("IC_or_Passport_No"));
                txtContactNum.setText(rs.getString("Contact_Number"));
                txtEmail.setText(rs.getString("Email"));
                txtPassword.setText(rs.getString("Password"));
                txtHouseAdd.setText(rs.getString("Home_Address"));
                dropListPosition.setSelectedItem(rs.getString("Position"));
                dropListDepartment.setSelectedItem(rs.getString("Department"));
                txtBasicSalary.setText(rs.getString("Basic_Salary"));
                txtBankAcc.setText(rs.getString("Bank_Account_No"));
                txtEPF.setText(rs.getString("EPF_Number"));
                txtTax.setText(rs.getString("Socso_or_Tax_ID"));
              

                setFieldsEditable(false);
            }

            rs.close();
            stmt.close();
            conn.close();
        } catch (Exception e) {
            e.printStackTrace();
            javax.swing.JOptionPane.showMessageDialog(this, "Error loading data: " + e.getMessage());
        }
    }

    
    
    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        txtPassword = new javax.swing.JTextField();
        lblPassword = new javax.swing.JLabel();
        lblHouseAdd = new javax.swing.JLabel();
        jScrollPane1 = new javax.swing.JScrollPane();
        txtHouseAdd = new javax.swing.JTextArea();
        lblPosition = new javax.swing.JLabel();
        lblFName = new javax.swing.JLabel();
        txtLName = new javax.swing.JTextField();
        lblLName = new javax.swing.JLabel();
        txtFName = new javax.swing.JTextField();
        lblWelcome = new javax.swing.JLabel();
        dropListPosition = new javax.swing.JComboBox<>();
        lblICNum = new javax.swing.JLabel();
        txtICNum = new javax.swing.JTextField();
        lblBankAcc = new javax.swing.JLabel();
        btnSave = new javax.swing.JButton();
        txtContactNum = new javax.swing.JTextField();
        dropListDepartment = new javax.swing.JComboBox<>();
        lblEPF = new javax.swing.JLabel();
        lblDepartment = new javax.swing.JLabel();
        txtEPF = new javax.swing.JTextField();
        btnEdit = new javax.swing.JButton();
        txtTax = new javax.swing.JTextField();
        lblBasicSalary = new javax.swing.JLabel();
        lblTax = new javax.swing.JLabel();
        txtBasicSalary = new javax.swing.JTextField();
        lblContactNum = new javax.swing.JLabel();
        btnReset = new javax.swing.JButton();
        txtBankAcc = new javax.swing.JTextField();
        lblEmail = new javax.swing.JLabel();
        txtEmail = new javax.swing.JTextField();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        txtPassword.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtPasswordActionPerformed(evt);
            }
        });

        lblPassword.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblPassword.setText("Password:");

        lblHouseAdd.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblHouseAdd.setText("House Address:");

        txtHouseAdd.setColumns(20);
        txtHouseAdd.setRows(5);
        jScrollPane1.setViewportView(txtHouseAdd);

        lblPosition.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblPosition.setText("Position:");

        lblFName.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblFName.setText("First Name:");

        txtLName.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtLNameActionPerformed(evt);
            }
        });

        lblLName.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblLName.setText("Last Name:");

        txtFName.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtFNameActionPerformed(evt);
            }
        });

        lblWelcome.setBackground(new java.awt.Color(255, 204, 204));
        lblWelcome.setFont(new java.awt.Font("Menlo", 1, 36)); // NOI18N
        lblWelcome.setText("Welcome !");
        lblWelcome.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));

        dropListPosition.setModel(new javax.swing.DefaultComboBoxModel<>(new String[] { "Manager", "HR Executive", "Software Developer", "Sales Representative", "Admin", "General Staff" }));

        lblICNum.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblICNum.setText("IC / Passport Number:");

        txtICNum.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtICNumActionPerformed(evt);
            }
        });

        lblBankAcc.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblBankAcc.setText("Bank Account:");

        btnSave.setFont(new java.awt.Font("Menlo", 0, 18)); // NOI18N
        btnSave.setText("Save");
        btnSave.setBorder(new javax.swing.border.SoftBevelBorder(javax.swing.border.BevelBorder.RAISED));
        btnSave.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        btnSave.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnSaveActionPerformed(evt);
            }
        });

        txtContactNum.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtContactNumActionPerformed(evt);
            }
        });

        dropListDepartment.setModel(new javax.swing.DefaultComboBoxModel<>(new String[] { "Administration Department", "Finance Department", "Sales Department", "Marketing Department", "HR Department", "IT Department", "Operation Department" }));

        lblEPF.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblEPF.setText("EPF Number:");

        lblDepartment.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblDepartment.setText("Department:");

        txtEPF.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtEPFActionPerformed(evt);
            }
        });

        btnEdit.setFont(new java.awt.Font("Menlo", 0, 18)); // NOI18N
        btnEdit.setText("Edit");
        btnEdit.setBorder(new javax.swing.border.SoftBevelBorder(javax.swing.border.BevelBorder.RAISED));
        btnEdit.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        btnEdit.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnEditActionPerformed(evt);
            }
        });

        txtTax.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtTaxActionPerformed(evt);
            }
        });

        lblBasicSalary.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblBasicSalary.setText("Basic Salary:");

        lblTax.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblTax.setText("SOCSO/Tax ID:");

        txtBasicSalary.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtBasicSalaryActionPerformed(evt);
            }
        });

        lblContactNum.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblContactNum.setText("Contact Number:");

        btnReset.setFont(new java.awt.Font("Menlo", 0, 14)); // NOI18N
        btnReset.setText("Reset");
        btnReset.setBorder(new javax.swing.border.SoftBevelBorder(javax.swing.border.BevelBorder.RAISED));
        btnReset.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        btnReset.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnResetActionPerformed(evt);
            }
        });

        txtBankAcc.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtBankAccActionPerformed(evt);
            }
        });

        lblEmail.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblEmail.setText("Email:");

        txtEmail.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtEmailActionPerformed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGap(25, 25, 25)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(lblWelcome)
                        .addGap(669, 669, 669))
                    .addGroup(layout.createSequentialGroup()
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addComponent(lblHouseAdd)
                            .addComponent(lblPassword)
                            .addComponent(lblEmail)
                            .addComponent(lblContactNum)
                            .addComponent(lblICNum)
                            .addComponent(lblLName)
                            .addComponent(lblFName))
                        .addGap(37, 37, 37)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                                .addComponent(btnEdit, javax.swing.GroupLayout.PREFERRED_SIZE, 115, javax.swing.GroupLayout.PREFERRED_SIZE)
                                .addGap(156, 156, 156))
                            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                                    .addComponent(txtPassword, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtEmail, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtContactNum, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtICNum, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtLName, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtFName, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE))
                                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                                .addComponent(btnReset, javax.swing.GroupLayout.PREFERRED_SIZE, 59, javax.swing.GroupLayout.PREFERRED_SIZE)
                                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                                        .addGroup(layout.createSequentialGroup()
                                            .addComponent(lblPosition)
                                            .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                                            .addComponent(dropListPosition, javax.swing.GroupLayout.PREFERRED_SIZE, 190, javax.swing.GroupLayout.PREFERRED_SIZE))
                                        .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                                            .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                                .addComponent(lblBankAcc)
                                                .addComponent(lblEPF))
                                            .addGap(70, 70, 70)
                                            .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                                .addComponent(txtEPF, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                                .addComponent(txtBankAcc, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                                .addComponent(txtTax, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE))
                                            .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, 27, Short.MAX_VALUE))
                                        .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                                            .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                                .addComponent(lblDepartment)
                                                .addComponent(lblBasicSalary))
                                            .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                                            .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                                .addComponent(txtBasicSalary, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                                .addComponent(dropListDepartment, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)))
                                        .addComponent(btnSave, javax.swing.GroupLayout.Alignment.TRAILING, javax.swing.GroupLayout.PREFERRED_SIZE, 115, javax.swing.GroupLayout.PREFERRED_SIZE))
                                    .addComponent(lblTax))))))
                .addGap(53, 53, 53))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                .addContainerGap(16, Short.MAX_VALUE)
                .addComponent(lblWelcome)
                .addGap(29, 29, 29)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txtFName, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblFName)
                    .addComponent(lblPosition)
                    .addComponent(dropListPosition, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txtLName, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblLName)
                    .addComponent(lblDepartment)
                    .addComponent(dropListDepartment, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txtICNum, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblICNum)
                    .addComponent(lblBasicSalary)
                    .addComponent(txtBasicSalary, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(12, 12, 12)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txtContactNum, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblContactNum)
                    .addComponent(lblBankAcc)
                    .addComponent(txtBankAcc, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                        .addComponent(txtEmail, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addComponent(lblEmail))
                    .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                        .addComponent(lblEPF)
                        .addComponent(txtEPF, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)))
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGap(16, 16, 16)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                            .addComponent(lblTax)
                            .addComponent(txtTax, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                            .addComponent(txtPassword, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                            .addComponent(lblPassword)
                            .addComponent(btnReset))
                        .addGap(21, 21, 21)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                            .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                                .addComponent(btnEdit, javax.swing.GroupLayout.PREFERRED_SIZE, 35, javax.swing.GroupLayout.PREFERRED_SIZE)
                                .addComponent(btnSave, javax.swing.GroupLayout.PREFERRED_SIZE, 35, javax.swing.GroupLayout.PREFERRED_SIZE))
                            .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(60, 60, 60)
                        .addComponent(lblHouseAdd)
                        .addGap(33, 33, 33)))
                .addContainerGap())
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void txtPasswordActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtPasswordActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtPasswordActionPerformed

    private void txtLNameActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtLNameActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtLNameActionPerformed

    private void txtFNameActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtFNameActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtFNameActionPerformed

    private void txtICNumActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtICNumActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtICNumActionPerformed

    private void btnSaveActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnSaveActionPerformed
        // TODO add your handling code here:
        try {
            Class.forName("org.apache.derby.jdbc.ClientDriver"); // or ClientDriver if using network
            Connection conn = DriverManager.getConnection("**************************************", "app", "app");

            String sql = "UPDATE Employee SET First_Name=?, Last_Name=?, IC_or_Passport_No=?, Contact_Number=?, Home_Address=?, "
            + "Position=?, Department=?, Basic_Salary=?, Bank_Account_No=?, EPF_Number=?, Socso_or_Tax_ID=? "
            + "WHERE Email=?";

            PreparedStatement stmt = conn.prepareStatement(sql);
            stmt.setString(1, txtFName.getText());
            stmt.setString(2, txtLName.getText());
            stmt.setString(3, txtICNum.getText());
            stmt.setString(4, txtContactNum.getText());
            stmt.setString(5, txtHouseAdd.getText());
            stmt.setString(6, dropListPosition.getSelectedItem().toString());
            stmt.setString(7, dropListDepartment.getSelectedItem().toString());
            stmt.setDouble(8, Double.parseDouble(txtBasicSalary.getText()));
            stmt.setString(9, txtBankAcc.getText());
            stmt.setString(10, txtEPF.getText());
            stmt.setString(11, txtTax.getText());

            stmt.setString(12, txtEmail.getText());

            int rowsUpdated = stmt.executeUpdate();

            if (rowsUpdated > 0) {
                JOptionPane.showMessageDialog(this, "Details updated successfully!");
            } else {
                JOptionPane.showMessageDialog(this, "No changes made.");
            }

            setFieldsEditable(false);
            btnEdit.setEnabled(true);
            btnSave.setEnabled(false);

            conn.close();

        } catch (ClassNotFoundException e) {
            JOptionPane.showMessageDialog(this, "Database driver not found: " + e.getMessage());
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "Database error: " + e.getMessage());
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "Invalid salary format. Please enter a valid number.");
        }
    }//GEN-LAST:event_btnSaveActionPerformed

    private void txtContactNumActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtContactNumActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtContactNumActionPerformed

    private void txtEPFActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtEPFActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtEPFActionPerformed

    private void btnEditActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnEditActionPerformed
        // TODO add your handling code here:
        setFieldsEditable(true);

        btnEdit.setEnabled(false);  // disable Edit button to prevent repeat clicks
        btnSave.setEnabled(true);

        JOptionPane.showMessageDialog(this, "You can edit your details now. Remember to click Save after editing.");

    }//GEN-LAST:event_btnEditActionPerformed

    private void txtTaxActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtTaxActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtTaxActionPerformed

    private void txtBasicSalaryActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtBasicSalaryActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtBasicSalaryActionPerformed

    private void btnResetActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnResetActionPerformed
        // TODO add your handling code here:
        new ResetPassword(employeeID).setVisible(true); // use the correct variable
        this.dispose();
    }//GEN-LAST:event_btnResetActionPerformed

    private void txtBankAccActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtBankAccActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtBankAccActionPerformed

    private void txtEmailActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtEmailActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtEmailActionPerformed

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(EmployeePortal.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(EmployeePortal.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(EmployeePortal.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(EmployeePortal.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new EmployeePortal(null).setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton btnEdit;
    private javax.swing.JButton btnReset;
    private javax.swing.JButton btnSave;
    private javax.swing.JComboBox<String> dropListDepartment;
    private javax.swing.JComboBox<String> dropListPosition;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JLabel lblBankAcc;
    private javax.swing.JLabel lblBasicSalary;
    private javax.swing.JLabel lblContactNum;
    private javax.swing.JLabel lblDepartment;
    private javax.swing.JLabel lblEPF;
    private javax.swing.JLabel lblEmail;
    private javax.swing.JLabel lblFName;
    private javax.swing.JLabel lblHouseAdd;
    private javax.swing.JLabel lblICNum;
    private javax.swing.JLabel lblLName;
    private javax.swing.JLabel lblPassword;
    private javax.swing.JLabel lblPosition;
    private javax.swing.JLabel lblTax;
    private javax.swing.JLabel lblWelcome;
    private javax.swing.JTextField txtBankAcc;
    private javax.swing.JTextField txtBasicSalary;
    private javax.swing.JTextField txtContactNum;
    private javax.swing.JTextField txtEPF;
    private javax.swing.JTextField txtEmail;
    private javax.swing.JTextField txtFName;
    private javax.swing.JTextArea txtHouseAdd;
    private javax.swing.JTextField txtICNum;
    private javax.swing.JTextField txtLName;
    private javax.swing.JTextField txtPassword;
    private javax.swing.JTextField txtTax;
    // End of variables declaration//GEN-END:variables
}
