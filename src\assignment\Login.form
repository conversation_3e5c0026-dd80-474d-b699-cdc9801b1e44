<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="241" max="-2" attributes="0"/>
                      <Component id="lblLogin" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="216" max="-2" attributes="0"/>
                      <Component id="lblNoAccYet" min="-2" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace max="32767" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="1" attributes="0">
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="lblEmail" min="-2" max="-2" attributes="0"/>
                              <EmptySpace min="-2" pref="89" max="-2" attributes="0"/>
                              <Component id="txtEmail" min="-2" pref="199" max="-2" attributes="0"/>
                          </Group>
                          <Group type="102" alignment="0" attributes="0">
                              <Component id="lblPassword" min="-2" max="-2" attributes="0"/>
                              <EmptySpace min="-2" pref="51" max="-2" attributes="0"/>
                              <Group type="103" groupAlignment="0" attributes="0">
                                  <Component id="txtPassword" alignment="0" min="-2" pref="199" max="-2" attributes="0"/>
                                  <Component id="btnLogin" alignment="0" min="-2" pref="89" max="-2" attributes="0"/>
                              </Group>
                          </Group>
                      </Group>
                      <EmptySpace min="-2" pref="157" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="1" attributes="0">
                      <Component id="btnRegister" min="-2" pref="81" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="208" max="-2" attributes="0"/>
                  </Group>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="53" max="-2" attributes="0"/>
              <Component id="lblLogin" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="40" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txtEmail" alignment="3" min="-2" pref="37" max="-2" attributes="0"/>
                  <Component id="lblEmail" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="38" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txtPassword" alignment="3" min="-2" pref="37" max="-2" attributes="0"/>
                  <Component id="lblPassword" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="32767" attributes="0"/>
              <Component id="btnLogin" min="-2" pref="35" max="-2" attributes="0"/>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Component id="lblNoAccYet" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="btnRegister" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="44" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JLabel" name="lblNoAccYet">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Oriya MN" size="12" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="No account yet? Sign up here."/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lblLogin">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="cc" green="cc" red="ff" type="rgb"/>
        </Property>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Menlo" size="36" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Login"/>
        <Property name="cursor" type="java.awt.Cursor" editor="org.netbeans.modules.form.editors2.CursorEditor">
          <Color id="Default Cursor"/>
        </Property>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lblEmail">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Oriya MN" size="24" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Email: "/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lblPassword">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Oriya MN" size="24" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Password:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txtEmail">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txtEmailActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JTextField" name="txtPassword">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txtPasswordActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnLogin">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Menlo" size="18" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Login"/>
        <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
          <Border info="org.netbeans.modules.form.compat2.border.SoftBevelBorderInfo">
            <BevelBorder/>
          </Border>
        </Property>
        <Property name="cursor" type="java.awt.Cursor" editor="org.netbeans.modules.form.editors2.CursorEditor">
          <Color id="Default Cursor"/>
        </Property>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnLoginActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnRegister">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Menlo" size="12" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Register"/>
        <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
          <Border info="org.netbeans.modules.form.compat2.border.SoftBevelBorderInfo">
            <BevelBorder/>
          </Border>
        </Property>
        <Property name="cursor" type="java.awt.Cursor" editor="org.netbeans.modules.form.editors2.CursorEditor">
          <Color id="Default Cursor"/>
        </Property>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnRegisterActionPerformed"/>
      </Events>
    </Component>
  </SubComponents>
</Form>
