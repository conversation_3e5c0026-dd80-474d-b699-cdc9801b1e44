/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package assignment;

import java.sql.*;
import javax.swing.*;
/**
 *
 * <AUTHOR>
 */
public class Register extends javax.swing.JFrame {

    /**
     * Creates new form Register
     */
    public Register() {
        initComponents();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        lblPosition = new javax.swing.JLabel();
        dropListDepartment = new javax.swing.JComboBox<>();
        lblDepartment = new javax.swing.JLabel();
        dropListPosition = new javax.swing.JComboBox<>();
        lblBankAcc = new javax.swing.JLabel();
        txtContactNum1 = new javax.swing.JTextField();
        lblRegister = new javax.swing.JLabel();
        lblEPF = new javax.swing.JLabel();
        lblFName = new javax.swing.JLabel();
        txtEPF = new javax.swing.JTextField();
        txtLName = new javax.swing.JTextField();
        txtTax = new javax.swing.JTextField();
        lblLName = new javax.swing.JLabel();
        lblTax = new javax.swing.JLabel();
        txtFName = new javax.swing.JTextField();
        lblICNum = new javax.swing.JLabel();
        btnRegister = new javax.swing.JButton();
        btnLogin = new javax.swing.JButton();
        txtICNum = new javax.swing.JTextField();
        lblAlreadyHaveAcc = new javax.swing.JLabel();
        lblContactNum = new javax.swing.JLabel();
        txtBankAcc = new javax.swing.JTextField();
        lblEmail = new javax.swing.JLabel();
        txtEmail = new javax.swing.JTextField();
        txtPassword = new javax.swing.JTextField();
        lblPassword = new javax.swing.JLabel();
        lblHouseAdd = new javax.swing.JLabel();
        jScrollPane1 = new javax.swing.JScrollPane();
        txtHouseAdd = new javax.swing.JTextArea();

        setDefaultCloseOperation(javax.swing.WindowConstants.EXIT_ON_CLOSE);

        lblPosition.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblPosition.setText("Position:");

        dropListDepartment.setModel(new javax.swing.DefaultComboBoxModel<>(new String[] { "Administration Department", "Finance Department", "Sales Department", "Marketing Department", "HR Department", "IT Department", "Operation Department" }));

        lblDepartment.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblDepartment.setText("Department:");

        dropListPosition.setModel(new javax.swing.DefaultComboBoxModel<>(new String[] { "Manager", "HR Executive", "Software Developer", "Sales Representative", "Admin", "General Staff" }));

        lblBankAcc.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblBankAcc.setText("Bank Account:");

        txtContactNum1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtContactNum1ActionPerformed(evt);
            }
        });

        lblRegister.setBackground(new java.awt.Color(255, 204, 204));
        lblRegister.setFont(new java.awt.Font("Menlo", 1, 36)); // NOI18N
        lblRegister.setText("Register");
        lblRegister.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));

        lblEPF.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblEPF.setText("EPF Number:");

        lblFName.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblFName.setText("First Name:");

        txtEPF.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtEPFActionPerformed(evt);
            }
        });

        txtLName.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtLNameActionPerformed(evt);
            }
        });

        txtTax.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtTaxActionPerformed(evt);
            }
        });

        lblLName.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblLName.setText("Last Name:");

        lblTax.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblTax.setText("SOCSO/Tax ID:");

        txtFName.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtFNameActionPerformed(evt);
            }
        });

        lblICNum.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblICNum.setText("IC / Passport Number:");

        btnRegister.setFont(new java.awt.Font("Menlo", 0, 18)); // NOI18N
        btnRegister.setText("Register");
        btnRegister.setBorder(new javax.swing.border.SoftBevelBorder(javax.swing.border.BevelBorder.RAISED));
        btnRegister.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        btnRegister.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnRegisterActionPerformed(evt);
            }
        });

        btnLogin.setFont(new java.awt.Font("Menlo", 0, 12)); // NOI18N
        btnLogin.setText("Login");
        btnLogin.setBorder(new javax.swing.border.SoftBevelBorder(javax.swing.border.BevelBorder.RAISED));
        btnLogin.setCursor(new java.awt.Cursor(java.awt.Cursor.DEFAULT_CURSOR));
        btnLogin.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                btnLoginActionPerformed(evt);
            }
        });

        txtICNum.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtICNumActionPerformed(evt);
            }
        });

        lblAlreadyHaveAcc.setFont(new java.awt.Font("Oriya MN", 0, 12)); // NOI18N
        lblAlreadyHaveAcc.setText("Already have account?");

        lblContactNum.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblContactNum.setText("Contact Number:");

        txtBankAcc.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtBankAccActionPerformed(evt);
            }
        });

        lblEmail.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblEmail.setText("Email:");

        txtEmail.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtEmailActionPerformed(evt);
            }
        });

        txtPassword.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                txtPasswordActionPerformed(evt);
            }
        });

        lblPassword.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblPassword.setText("Password:");

        lblHouseAdd.setFont(new java.awt.Font("Oriya MN", 0, 14)); // NOI18N
        lblHouseAdd.setText("House Address:");

        txtHouseAdd.setColumns(20);
        txtHouseAdd.setRows(5);
        jScrollPane1.setViewportView(txtHouseAdd);

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGap(87, 87, 87)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(lblLName)
                                    .addComponent(lblFName))
                                .addGap(100, 100, 100)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING)
                                    .addComponent(txtLName, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtFName, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)))
                            .addGroup(layout.createSequentialGroup()
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(lblContactNum)
                                    .addComponent(lblEmail)
                                    .addComponent(lblPassword)
                                    .addComponent(lblHouseAdd)
                                    .addComponent(lblICNum))
                                .addGap(30, 30, 30)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(txtICNum, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtEmail, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtPassword, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtContactNum1, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE))))
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                            .addGroup(layout.createSequentialGroup()
                                .addGap(58, 58, 58)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(lblPosition)
                                    .addComponent(lblDepartment)
                                    .addComponent(lblBankAcc)
                                    .addComponent(lblEPF)
                                    .addComponent(lblTax))
                                .addGap(31, 31, 31)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                                    .addComponent(txtEPF, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtBankAcc, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(dropListDepartment, 0, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                                    .addComponent(dropListPosition, javax.swing.GroupLayout.PREFERRED_SIZE, 180, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addComponent(txtTax, javax.swing.GroupLayout.PREFERRED_SIZE, 163, javax.swing.GroupLayout.PREFERRED_SIZE)))
                            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                                    .addComponent(btnRegister, javax.swing.GroupLayout.Alignment.TRAILING, javax.swing.GroupLayout.PREFERRED_SIZE, 115, javax.swing.GroupLayout.PREFERRED_SIZE)
                                    .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                                        .addComponent(lblAlreadyHaveAcc)
                                        .addGap(15, 15, 15)
                                        .addComponent(btnLogin, javax.swing.GroupLayout.PREFERRED_SIZE, 49, javax.swing.GroupLayout.PREFERRED_SIZE))))))
                    .addGroup(layout.createSequentialGroup()
                        .addGap(303, 303, 303)
                        .addComponent(lblRegister)))
                .addContainerGap(163, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGap(22, 22, 22)
                .addComponent(lblRegister)
                .addGap(35, 35, 35)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(lblFName)
                    .addComponent(txtFName, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblPosition)
                    .addComponent(dropListPosition, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(lblLName)
                    .addComponent(txtLName, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblDepartment)
                    .addComponent(dropListDepartment, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(21, 21, 21)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(lblICNum)
                    .addComponent(txtICNum, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblBankAcc)
                    .addComponent(txtBankAcc, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(lblContactNum)
                    .addComponent(txtContactNum1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblEPF)
                    .addComponent(txtEPF, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE))
                .addGap(21, 21, 21)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txtEmail, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblEmail)
                    .addComponent(txtTax, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblTax))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(txtPassword, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(lblPassword))
                .addGap(18, 18, 18)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(lblHouseAdd)
                    .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(btnRegister, javax.swing.GroupLayout.PREFERRED_SIZE, 35, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addGap(18, 18, 18)
                        .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                            .addComponent(btnLogin)
                            .addComponent(lblAlreadyHaveAcc))))
                .addContainerGap(24, Short.MAX_VALUE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void txtContactNum1ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtContactNum1ActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtContactNum1ActionPerformed

    private void txtEPFActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtEPFActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtEPFActionPerformed

    private void txtLNameActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtLNameActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtLNameActionPerformed

    private void txtTaxActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtTaxActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtTaxActionPerformed

    private void txtFNameActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtFNameActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtFNameActionPerformed

    private void btnRegisterActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnRegisterActionPerformed
        // TODO add your handling code here:
        String fName = txtFName.getText().trim();
        String lName = txtLName.getText().trim();
        String icNum = txtICNum.getText().trim();
        String contactNum = txtContactNum1.getText().trim();
        String email = txtEmail.getText().trim();
        String password = txtPassword.getText().trim();
        String houseAddress = txtHouseAdd.getText().trim();
        String department = dropListDepartment.getSelectedItem().toString().trim();
        String position = dropListPosition.getSelectedItem().toString().trim();
        String bankAcc = txtBankAcc.getText().trim();
        String epf = txtEPF.getText().trim();
        String socsoTax = txtTax.getText().trim();

        if (fName.isEmpty() || lName.isEmpty() || icNum.isEmpty() || contactNum.isEmpty() ||
            email.isEmpty() || password.isEmpty() || houseAddress.isEmpty() ||
            bankAcc.isEmpty() || epf.isEmpty() || socsoTax.isEmpty()) {
            JOptionPane.showMessageDialog(this, "⚠️ All fields are required.", "Missing Information", JOptionPane.WARNING_MESSAGE);
            return; // Stop here if any field is empty
        }

        try {
            Class.forName("org.apache.derby.jdbc.ClientDriver");
            Connection conn = DriverManager.getConnection("**************************************", "app", "app");

            String checkEmailSql = "SELECT * FROM Employee WHERE Email = ?";
            PreparedStatement checkEmailStmt = conn.prepareStatement(checkEmailSql);
            checkEmailStmt.setString(1, email);
            ResultSet emailResult = checkEmailStmt.executeQuery();

            if (emailResult.next()) {
                JOptionPane.showMessageDialog(this, "⚠️ This email is already registered. Please use a different email.", "Duplicate Email", JOptionPane.WARNING_MESSAGE);
                emailResult.close();
                checkEmailStmt.close();
                conn.close();
                return;
            }

            emailResult.close();
            checkEmailStmt.close();

            // Count existing employees to generate the next employeeID
            String getMaxIdSql = "SELECT Employee_ID FROM Employee ORDER BY Employee_ID DESC FETCH FIRST ROW ONLY";
            Statement stmtMax = conn.createStatement();
            ResultSet rs = stmtMax.executeQuery(getMaxIdSql);

            String employeeID;
            if (rs.next()) {
                String lastID = rs.getString("Employee_ID"); // e.g., EMP007
                System.out.println("DEBUG - Last ID: " + lastID);

                if (lastID != null && lastID.length() >= 4) {
                    int id = Integer.parseInt(lastID.substring(3));
                    employeeID = String.format("EMP%03d", id + 1);  // EMP008
                } else {
                    employeeID = "EMP001"; // fallback
                }
            } else {
                employeeID = "EMP001"; // table is empty
            }

            rs.close();
            stmtMax.close();

            // Insert data into your Employee table
            String sql = "INSERT INTO Employee (First_Name, Last_Name, IC_or_Passport_No, Contact_Number, Email, Password, Home_Address, Position, Department, Basic_Salary, Bank_Account_No, EPF_Number, Socso_or_Tax_ID) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            PreparedStatement stmt = conn.prepareStatement(sql);

            stmt.setString(1, fName);
            stmt.setString(2, lName);
            stmt.setString(3, icNum);
            stmt.setString(4, contactNum);
            stmt.setString(5, email);
            stmt.setString(6, password);
            stmt.setString(7, houseAddress);
            stmt.setString(9, position);
            stmt.setString(8, department);
            stmt.setBigDecimal(10, new java.math.BigDecimal("0.00")); // Editable later by admin
            stmt.setString(11, bankAcc);
            stmt.setString(12, epf);
            stmt.setString(13, socsoTax);

            int rows = stmt.executeUpdate();

            if (rows > 0) {
                JOptionPane.showMessageDialog(this, "✅ Registration successful!\nGenerated Employee ID: " + employeeID);

                // Clear all fields
                txtFName.setText("");
                txtLName.setText("");
                txtICNum.setText("");
                txtContactNum1.setText(""); // assuming txtContactNum1 is your contact number field
                txtEmail.setText("");
                txtPassword.setText("");
                txtHouseAdd.setText("");
                dropListPosition.setSelectedIndex(0);
                dropListDepartment.setSelectedIndex(0);
                txtBankAcc.setText("");
                txtEPF.setText("");
                txtTax.setText("");

            } else {
                JOptionPane.showMessageDialog(this, "❌ Registration failed.");
            }

            stmt.close();
            conn.close();

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "❗ Error: " + e.getMessage());
        }
    }//GEN-LAST:event_btnRegisterActionPerformed

    private void btnLoginActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_btnLoginActionPerformed
        new Login().setVisible(true);
        this.dispose();

    }//GEN-LAST:event_btnLoginActionPerformed

    private void txtICNumActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtICNumActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtICNumActionPerformed

    private void txtBankAccActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtBankAccActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtBankAccActionPerformed

    private void txtEmailActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtEmailActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtEmailActionPerformed

    private void txtPasswordActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_txtPasswordActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_txtPasswordActionPerformed

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(Register.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(Register.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(Register.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(Register.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new Register().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JButton btnLogin;
    private javax.swing.JButton btnRegister;
    private javax.swing.JComboBox<String> dropListDepartment;
    private javax.swing.JComboBox<String> dropListPosition;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JLabel lblAlreadyHaveAcc;
    private javax.swing.JLabel lblBankAcc;
    private javax.swing.JLabel lblContactNum;
    private javax.swing.JLabel lblDepartment;
    private javax.swing.JLabel lblEPF;
    private javax.swing.JLabel lblEmail;
    private javax.swing.JLabel lblFName;
    private javax.swing.JLabel lblHouseAdd;
    private javax.swing.JLabel lblICNum;
    private javax.swing.JLabel lblLName;
    private javax.swing.JLabel lblPassword;
    private javax.swing.JLabel lblPosition;
    private javax.swing.JLabel lblRegister;
    private javax.swing.JLabel lblTax;
    private javax.swing.JTextField txtBankAcc;
    private javax.swing.JTextField txtContactNum1;
    private javax.swing.JTextField txtEPF;
    private javax.swing.JTextField txtEmail;
    private javax.swing.JTextField txtFName;
    private javax.swing.JTextArea txtHouseAdd;
    private javax.swing.JTextField txtICNum;
    private javax.swing.JTextField txtLName;
    private javax.swing.JTextField txtPassword;
    private javax.swing.JTextField txtTax;
    // End of variables declaration//GEN-END:variables
}
