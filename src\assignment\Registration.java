/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package assignment;

import java.sql.*;
import java.util.Scanner;

/**
 *
 * <AUTHOR>
 */
public class Registration {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // Get employee details from terminal
        System.out.print("Enter First Name: ");
        String firstName = scanner.nextLine();

        System.out.print("Enter Last Name: ");
        String lastName = scanner.nextLine();

        System.out.print("Enter IC or Passport Number: ");
        String ic = scanner.nextLine();

        System.out.print("Enter Contact Number: ");
        String contact = scanner.nextLine();

        System.out.print("Enter Email: ");
        String email = scanner.nextLine();

        System.out.print("Enter Password: ");
        String password = scanner.nextLine();

        System.out.print("Enter Home Address: ");
        String address = scanner.nextLine();

        System.out.print("Enter Position: ");
        String position = scanner.nextLine();

        System.out.print("Enter Department: ");
        String department = scanner.nextLine();

        System.out.print("Enter Basic Salary: ");
        double basicSalary = Double.parseDouble(scanner.nextLine());

        System.out.print("Enter Bank Account No: ");
        String bankAcc = scanner.nextLine();

        System.out.print("Enter EPF Number: ");
        String epf = scanner.nextLine();

        System.out.print("Enter SOCSO or Tax ID: ");
        String socso = scanner.nextLine();

        // Connect to DB and insert data
        try {
            // 1. Load Derby JDBC driver
            Class.forName("org.apache.derby.jdbc.ClientDriver");

            // 2. Connect to Derby
            Connection conn = DriverManager.getConnection(
                "**************************************", "app", "app"
            );

            // 3. Prepare SQL insert (without Employee_ID, since it’s auto-generated)
            String sql = "INSERT INTO Employee (First_Name, Last_Name, IC_or_Passport_No, Contact_Number, Email, Password, Home_Address, Position, Department, Basic_Salary, Bank_Account_No, EPF_Number, Socso_or_Tax_ID) " +
                         "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            stmt.setString(1, firstName);
            stmt.setString(2, lastName);
            stmt.setString(3, ic);
            stmt.setString(4, contact);
            stmt.setString(5, email);
            stmt.setString(6, password);
            stmt.setString(7, address);
            stmt.setString(8, position);
            stmt.setString(9, department);
            stmt.setDouble(10, basicSalary);
            stmt.setString(11, bankAcc);
            stmt.setString(12, epf);
            stmt.setString(13, socso);

            // 4. Execute
            int rows = stmt.executeUpdate();

            if (rows > 0) {
                ResultSet generatedKeys = stmt.getGeneratedKeys();
                if (generatedKeys.next()) {
                    int autoId = generatedKeys.getInt(1); // get auto-generated ID
                    String employeeId = "EMP" + String.format("%03d", autoId); // Format like EMP001, EMP002
                    System.out.println("✅ Employee registered successfully! Assigned ID: " + employeeId);
                } else {
                    System.out.println("⚠️ Inserted, but failed to retrieve Employee ID.");
                }
            } else {
                System.out.println("❌ Employee registration failed.");
            }

            // 5. Clean up
            stmt.close();
            conn.close();

        } catch (Exception e) {
            e.printStackTrace();
        }

        scanner.close();
    }
}
