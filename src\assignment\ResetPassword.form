<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="53" max="-2" attributes="0"/>
              <Component id="lblInfo" min="-2" max="-2" attributes="0"/>
              <EmptySpace pref="589" max="32767" attributes="0"/>
          </Group>
          <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="138" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="0" attributes="0">
                      <Group type="102" alignment="0" attributes="0">
                          <Group type="103" groupAlignment="0" attributes="0">
                              <Group type="102" alignment="0" attributes="0">
                                  <EmptySpace min="-2" pref="61" max="-2" attributes="0"/>
                                  <Component id="lblResetPass" max="-2" attributes="0"/>
                              </Group>
                              <Group type="102" alignment="0" attributes="0">
                                  <EmptySpace min="-2" pref="184" max="-2" attributes="0"/>
                                  <Component id="btnReset" min="-2" pref="59" max="-2" attributes="0"/>
                              </Group>
                          </Group>
                          <EmptySpace pref="31" max="-2" attributes="0"/>
                      </Group>
                      <Group type="102" alignment="1" attributes="0">
                          <Group type="103" groupAlignment="0" attributes="0">
                              <Component id="lblNewPass" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="lblConfirmPass" alignment="0" min="-2" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace min="-2" pref="44" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="0" max="-2" attributes="0">
                              <Component id="txtNewPass" alignment="0" max="32767" attributes="0"/>
                              <Component id="txtConfirmPass" alignment="0" pref="174" max="32767" attributes="0"/>
                          </Group>
                      </Group>
                  </Group>
                  <EmptySpace pref="139" max="32767" attributes="0"/>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="65" max="-2" attributes="0"/>
              <Component id="lblInfo" min="-2" max="-2" attributes="0"/>
              <EmptySpace pref="284" max="32767" attributes="0"/>
          </Group>
          <Group type="103" rootIndex="1" groupAlignment="0" attributes="0">
              <Group type="102" attributes="0">
                  <EmptySpace min="-2" pref="54" max="-2" attributes="0"/>
                  <Component id="lblResetPass" max="32767" attributes="0"/>
                  <EmptySpace min="42" pref="42" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="txtNewPass" alignment="3" min="-2" pref="31" max="-2" attributes="0"/>
                      <Component id="lblNewPass" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="40" max="-2" attributes="0"/>
                  <Group type="103" groupAlignment="3" attributes="0">
                      <Component id="txtConfirmPass" alignment="3" min="-2" pref="31" max="-2" attributes="0"/>
                      <Component id="lblConfirmPass" alignment="3" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <EmptySpace min="-2" pref="41" max="-2" attributes="0"/>
                  <Component id="btnReset" min="-2" max="-2" attributes="0"/>
                  <EmptySpace min="-2" pref="54" max="-2" attributes="0"/>
              </Group>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Component class="javax.swing.JPasswordField" name="txtConfirmPass">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txtConfirmPassActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnReset">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Menlo" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Reset"/>
        <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
          <Border info="org.netbeans.modules.form.compat2.border.SoftBevelBorderInfo">
            <BevelBorder/>
          </Border>
        </Property>
        <Property name="cursor" type="java.awt.Cursor" editor="org.netbeans.modules.form.editors2.CursorEditor">
          <Color id="Default Cursor"/>
        </Property>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnResetActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="lblResetPass">
      <Properties>
        <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
          <Color blue="cc" green="cc" red="ff" type="rgb"/>
        </Property>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Menlo" size="36" style="1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Reset Password"/>
        <Property name="cursor" type="java.awt.Cursor" editor="org.netbeans.modules.form.editors2.CursorEditor">
          <Color id="Default Cursor"/>
        </Property>
      </Properties>
    </Component>
    <Component class="javax.swing.JPasswordField" name="txtNewPass">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txtNewPassActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="lblNewPass">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Oriya MN" size="18" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="New Password:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lblConfirmPass">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Oriya MN" size="18" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Confirm Password:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="lblInfo">
      <Properties>
        <Property name="text" type="java.lang.String" value="."/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
