/*
 * Database Manager for Hotel Management System
 * Handles Derby database connections and operations
 */
package test1;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Database Manager class for handling Derby database operations
 * 
 * <AUTHOR>
 */
public class DatabaseManager {
    
    // Database connection details
    private static final String DB_URL = "******************************";
    private static final String DRIVER = "org.apache.derby.jdbc.EmbeddedDriver";
    
    // Singleton instance
    private static DatabaseManager instance;
    private Connection connection;
    
    /**
     * Private constructor for singleton pattern
     */
    private DatabaseManager() {
        try {
            // Load Derby driver
            Class.forName(DRIVER);
            // Establish connection
            connection = DriverManager.getConnection(DB_URL);
            System.out.println("Connected to Derby database successfully!");
            
            // Create tables if they don't exist
            createTables();
            
        } catch (ClassNotFoundException e) {
            System.err.println("Derby driver not found: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("Database connection error: " + e.getMessage());
        }
    }
    
    /**
     * Get singleton instance of DatabaseManager
     * 
     * @return DatabaseManager instance
     */
    public static synchronized DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }
    
    /**
     * Get database connection
     * 
     * @return Connection object
     */
    public Connection getConnection() {
        try {
            if (connection == null || connection.isClosed()) {
                connection = DriverManager.getConnection(DB_URL);
            }
        } catch (SQLException e) {
            System.err.println("Error getting connection: " + e.getMessage());
        }
        return connection;
    }
    
    /**
     * Create necessary tables for the hotel management system
     */
    private void createTables() {
        try {
            Statement stmt = connection.createStatement();
            
            // Create Users table for registration
            String createUsersTable = """
                CREATE TABLE Users (
                    id INTEGER NOT NULL GENERATED ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1),
                    username VARCHAR(50) NOT NULL UNIQUE,
                    firstName VARCHAR(50) NOT NULL,
                    lastName VARCHAR(50) NOT NULL,
                    icNumber VARCHAR(20) NOT NULL UNIQUE,
                    registrationDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id)
                )
            """;
            
            // Create Guests table for hotel guests
            String createGuestsTable = """
                CREATE TABLE Guests (
                    id INTEGER NOT NULL GENERATED ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1),
                    firstName VARCHAR(50) NOT NULL,
                    lastName VARCHAR(50) NOT NULL,
                    email VARCHAR(100),
                    phone VARCHAR(20),
                    address VARCHAR(200),
                    idNumber VARCHAR(20) NOT NULL,
                    checkInDate DATE,
                    checkOutDate DATE,
                    roomNumber INTEGER,
                    status VARCHAR(20) DEFAULT 'ACTIVE',
                    PRIMARY KEY (id)
                )
            """;
            
            // Create Rooms table
            String createRoomsTable = """
                CREATE TABLE Rooms (
                    roomNumber INTEGER NOT NULL,
                    roomType VARCHAR(20) NOT NULL,
                    price DECIMAL(10,2) NOT NULL,
                    status VARCHAR(20) DEFAULT 'AVAILABLE',
                    description VARCHAR(200),
                    PRIMARY KEY (roomNumber)
                )
            """;
            
            // Create Bookings table
            String createBookingsTable = """
                CREATE TABLE Bookings (
                    id INTEGER NOT NULL GENERATED ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1),
                    guestId INTEGER NOT NULL,
                    roomNumber INTEGER NOT NULL,
                    checkInDate DATE NOT NULL,
                    checkOutDate DATE NOT NULL,
                    totalAmount DECIMAL(10,2),
                    status VARCHAR(20) DEFAULT 'CONFIRMED',
                    bookingDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    FOREIGN KEY (guestId) REFERENCES Guests(id),
                    FOREIGN KEY (roomNumber) REFERENCES Rooms(roomNumber)
                )
            """;
            
            // Try to create each table, ignore if already exists
            try {
                stmt.executeUpdate(createUsersTable);
                System.out.println("Users table created successfully!");
            } catch (SQLException e) {
                if (!e.getSQLState().equals("X0Y32")) { // Table already exists
                    System.err.println("Error creating Users table: " + e.getMessage());
                }
            }
            
            try {
                stmt.executeUpdate(createGuestsTable);
                System.out.println("Guests table created successfully!");
            } catch (SQLException e) {
                if (!e.getSQLState().equals("X0Y32")) {
                    System.err.println("Error creating Guests table: " + e.getMessage());
                }
            }
            
            try {
                stmt.executeUpdate(createRoomsTable);
                System.out.println("Rooms table created successfully!");
                // Insert some sample rooms
                insertSampleRooms();
            } catch (SQLException e) {
                if (!e.getSQLState().equals("X0Y32")) {
                    System.err.println("Error creating Rooms table: " + e.getMessage());
                }
            }
            
            try {
                stmt.executeUpdate(createBookingsTable);
                System.out.println("Bookings table created successfully!");
            } catch (SQLException e) {
                if (!e.getSQLState().equals("X0Y32")) {
                    System.err.println("Error creating Bookings table: " + e.getMessage());
                }
            }
            
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error creating tables: " + e.getMessage());
        }
    }
    
    /**
     * Insert sample room data
     */
    private void insertSampleRooms() {
        String insertRoom = "INSERT INTO Rooms (roomNumber, roomType, price, status, description) VALUES (?, ?, ?, ?, ?)";
        
        try {
            PreparedStatement pstmt = connection.prepareStatement(insertRoom);
            
            // Sample room data
            Object[][] rooms = {
                {101, "SINGLE", 80.00, "AVAILABLE", "Standard single room with city view"},
                {102, "SINGLE", 80.00, "OCCUPIED", "Standard single room with city view"},
                {201, "DOUBLE", 120.00, "AVAILABLE", "Double room with garden view"},
                {202, "DOUBLE", 120.00, "AVAILABLE", "Double room with garden view"},
                {301, "SUITE", 250.00, "AVAILABLE", "Luxury suite with ocean view"},
                {302, "SUITE", 250.00, "MAINTENANCE", "Luxury suite with ocean view"},
                {401, "DELUXE", 180.00, "AVAILABLE", "Deluxe room with balcony"},
                {402, "DELUXE", 180.00, "OCCUPIED", "Deluxe room with balcony"}
            };
            
            for (Object[] room : rooms) {
                pstmt.setInt(1, (Integer) room[0]);
                pstmt.setString(2, (String) room[1]);
                pstmt.setDouble(3, (Double) room[2]);
                pstmt.setString(4, (String) room[3]);
                pstmt.setString(5, (String) room[4]);
                
                try {
                    pstmt.executeUpdate();
                } catch (SQLException e) {
                    // Ignore duplicate key errors
                    if (!e.getSQLState().equals("23505")) {
                        System.err.println("Error inserting room " + room[0] + ": " + e.getMessage());
                    }
                }
            }
            
            pstmt.close();
            System.out.println("Sample rooms inserted successfully!");
            
        } catch (SQLException e) {
            System.err.println("Error inserting sample rooms: " + e.getMessage());
        }
    }
    
    /**
     * Register a new user
     * 
     * @param username User's username
     * @param firstName User's first name
     * @param lastName User's last name
     * @param icNumber User's IC number
     * @return true if registration successful, false otherwise
     */
    public boolean registerUser(String username, String firstName, String lastName, String icNumber) {
        String sql = "INSERT INTO Users (username, firstName, lastName, icNumber) VALUES (?, ?, ?, ?)";
        
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, firstName);
            pstmt.setString(3, lastName);
            pstmt.setString(4, icNumber);
            
            int rowsAffected = pstmt.executeUpdate();
            pstmt.close();
            
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("Error registering user: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get all users from database
     * 
     * @return List of user data
     */
    public List<String[]> getAllUsers() {
        List<String[]> users = new ArrayList<>();
        String sql = "SELECT username, firstName, lastName, icNumber, registrationDate FROM Users ORDER BY registrationDate DESC";
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                String[] user = {
                    rs.getString("username"),
                    rs.getString("firstName"),
                    rs.getString("lastName"),
                    rs.getString("icNumber"),
                    rs.getTimestamp("registrationDate").toString()
                };
                users.add(user);
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error getting users: " + e.getMessage());
        }
        
        return users;
    }
    
    /**
     * Get room statistics for dashboard
     *
     * @return int array with [totalRooms, occupiedRooms, availableRooms, maintenanceRooms]
     */
    public int[] getRoomStatistics() {
        int[] stats = new int[4]; // total, occupied, available, maintenance
        String sql = "SELECT status, COUNT(*) as count FROM Rooms GROUP BY status";

        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                String status = rs.getString("status");
                int count = rs.getInt("count");

                switch (status.toUpperCase()) {
                    case "OCCUPIED":
                        stats[1] = count;
                        break;
                    case "AVAILABLE":
                        stats[2] = count;
                        break;
                    case "MAINTENANCE":
                        stats[3] = count;
                        break;
                }
            }

            // Calculate total rooms
            stats[0] = stats[1] + stats[2] + stats[3];

            rs.close();
            stmt.close();

        } catch (SQLException e) {
            System.err.println("Error getting room statistics: " + e.getMessage());
            // Return default values if error
            stats[0] = 8; // total
            stats[1] = 2; // occupied
            stats[2] = 5; // available
            stats[3] = 1; // maintenance
        }

        return stats;
    }

    /**
     * Get total number of registered guests
     *
     * @return Number of registered guests
     */
    public int getTotalGuests() {
        String sql = "SELECT COUNT(*) as total FROM Users";

        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            if (rs.next()) {
                int total = rs.getInt("total");
                rs.close();
                stmt.close();
                return total;
            }

        } catch (SQLException e) {
            System.err.println("Error getting guest count: " + e.getMessage());
        }

        return 0;
    }

    /**
     * Close database connection
     */
    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                System.out.println("Database connection closed.");
            }
        } catch (SQLException e) {
            System.err.println("Error closing connection: " + e.getMessage());
        }
    }
}
