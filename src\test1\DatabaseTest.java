/*
 * Database Test Class
 * Simple test to verify Derby database connectivity
 */
package test1;

/**
 * Test class to verify database functionality
 * 
 * <AUTHOR>
 */
public class DatabaseTest {
    
    public static void main(String[] args) {
        System.out.println("=== Hotel Management System Database Test ===");
        
        try {
            // Test direct connection first
            testDirectConnection();

            // Get database manager instance
            NetBeansDatabaseManager dbManager = NetBeansDatabaseManager.getInstance();

            // Test database connection
            if (dbManager.getConnection() != null) {
                System.out.println("✓ Database manager connection successful!");
                System.out.println("  Connection: " + dbManager.getConnection().toString());
            } else {
                System.out.println("✗ Database manager connection failed!");
                return;
            }
            
            // Test room statistics
            int[] roomStats = dbManager.getRoomStatistics();
            System.out.println("\n=== Room Statistics ===");
            System.out.println("Total Rooms: " + roomStats[0]);
            System.out.println("Occupied Rooms: " + roomStats[1]);
            System.out.println("Available Rooms: " + roomStats[2]);
            System.out.println("Maintenance Rooms: " + roomStats[3]);
            
            // Test guest count
            int guestCount = dbManager.getTotalGuests();
            System.out.println("\n=== Guest Statistics ===");
            System.out.println("Total Registered Guests: " + guestCount);
            
            // Test user registration
            System.out.println("\n=== Testing User Registration ===");
            boolean testRegistration = dbManager.registerUser(
                "testuser_" + System.currentTimeMillis(), 
                "Test", 
                "User", 
                "TEST" + System.currentTimeMillis()
            );
            
            if (testRegistration) {
                System.out.println("✓ Test user registration successful!");
            } else {
                System.out.println("✗ Test user registration failed!");
            }
            
            // Get all users
            java.util.List<String[]> users = dbManager.getAllUsers();
            System.out.println("\n=== All Registered Users ===");
            System.out.println("Total users in database: " + users.size());
            
            if (!users.isEmpty()) {
                System.out.println("Recent users:");
                for (int i = 0; i < Math.min(3, users.size()); i++) {
                    String[] user = users.get(i);
                    System.out.println("- " + user[1] + " " + user[2] + " (" + user[0] + ")");
                }
            }
            
            System.out.println("\n=== Database Test Completed Successfully! ===");
            
        } catch (Exception e) {
            System.err.println("Database test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test direct database connection
     */
    private static void testDirectConnection() {
        System.out.println("\n--- Direct Connection Test ---");

        String url = "*************************************";
        java.sql.Connection conn = null;

        try {
            System.out.println("Attempting direct connection to: " + url);

            // Try to connect directly
            conn = java.sql.DriverManager.getConnection(url);
            System.out.println("✓ Direct connection successful!");

            // Test if we can query the EMPLOYEES table
            java.sql.Statement stmt = conn.createStatement();
            java.sql.ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as total FROM EMPLOYEES");

            if (rs.next()) {
                int count = rs.getInt("total");
                System.out.println("✓ EMPLOYEES table accessible, current count: " + count);
            }

            // Test if we can insert data
            String insertSql = "INSERT INTO EMPLOYEES (USERNAME, FIRST_NAME, LAST_NAME, IC_PASSPORT) VALUES (?, ?, ?, ?)";
            java.sql.PreparedStatement pstmt = conn.prepareStatement(insertSql);
            pstmt.setString(1, "test_direct");
            pstmt.setString(2, "Direct");
            pstmt.setString(3, "Test");
            pstmt.setString(4, "TEST123");

            int rowsAffected = pstmt.executeUpdate();
            System.out.println("✓ Insert test successful, rows affected: " + rowsAffected);

            // Check count again
            rs = stmt.executeQuery("SELECT COUNT(*) as total FROM EMPLOYEES");
            if (rs.next()) {
                int newCount = rs.getInt("total");
                System.out.println("✓ New count after insert: " + newCount);
            }

            rs.close();
            stmt.close();
            pstmt.close();

        } catch (java.sql.SQLException e) {
            System.err.println("✗ Direct connection failed: " + e.getMessage());
            System.err.println("  SQL State: " + e.getSQLState());
            System.err.println("  Error Code: " + e.getErrorCode());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (java.sql.SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
        }
    }
}
