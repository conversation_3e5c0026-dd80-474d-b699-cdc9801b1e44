/*
 * Database Test Class
 * Simple test to verify Derby database connectivity
 */
package test1;

/**
 * Test class to verify database functionality
 * 
 * <AUTHOR>
 */
public class DatabaseTest {
    
    public static void main(String[] args) {
        System.out.println("=== Hotel Management System Database Test ===");
        
        try {
            // Get database manager instance
            DatabaseManager dbManager = DatabaseManager.getInstance();
            
            // Test database connection
            if (dbManager.getConnection() != null) {
                System.out.println("✓ Database connection successful!");
            } else {
                System.out.println("✗ Database connection failed!");
                return;
            }
            
            // Test room statistics
            int[] roomStats = dbManager.getRoomStatistics();
            System.out.println("\n=== Room Statistics ===");
            System.out.println("Total Rooms: " + roomStats[0]);
            System.out.println("Occupied Rooms: " + roomStats[1]);
            System.out.println("Available Rooms: " + roomStats[2]);
            System.out.println("Maintenance Rooms: " + roomStats[3]);
            
            // Test guest count
            int guestCount = dbManager.getTotalGuests();
            System.out.println("\n=== Guest Statistics ===");
            System.out.println("Total Registered Guests: " + guestCount);
            
            // Test user registration
            System.out.println("\n=== Testing User Registration ===");
            boolean testRegistration = dbManager.registerUser(
                "testuser_" + System.currentTimeMillis(), 
                "Test", 
                "User", 
                "TEST" + System.currentTimeMillis()
            );
            
            if (testRegistration) {
                System.out.println("✓ Test user registration successful!");
            } else {
                System.out.println("✗ Test user registration failed!");
            }
            
            // Get all users
            java.util.List<String[]> users = dbManager.getAllUsers();
            System.out.println("\n=== All Registered Users ===");
            System.out.println("Total users in database: " + users.size());
            
            if (!users.isEmpty()) {
                System.out.println("Recent users:");
                for (int i = 0; i < Math.min(3, users.size()); i++) {
                    String[] user = users.get(i);
                    System.out.println("- " + user[1] + " " + user[2] + " (" + user[0] + ")");
                }
            }
            
            System.out.println("\n=== Database Test Completed Successfully! ===");
            
        } catch (Exception e) {
            System.err.println("Database test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
