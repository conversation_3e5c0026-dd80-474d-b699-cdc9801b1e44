/*
 * Hello World Design Page
 */
package test1;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * A simple Hello World design page using Java Swing
 * 
 * <AUTHOR>
 */
public class HelloWorldPage extends J<PERSON>rame {
    
    // UI Components
    private JLabel titleLabel;
    private JLabel messageLabel;
    private JButton clickMeButton;
    private JPanel mainPanel;
    private JPanel buttonPanel;
    
    /**
     * Constructor for the HelloWorldPage
     */
    public HelloWorldPage() {
        // Set up the frame
        setTitle("Hello World Design");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(500, 400);
        setLocationRelativeTo(null); // Center on screen
        
        // Initialize components
        initComponents();
        
        // Add components to the frame
        add(mainPanel);
        
        // Make the frame visible
        setVisible(true);
    }
    
    /**
     * Initialize all UI components
     */
    private void initComponents() {
        // Create panels with layout managers
        mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        
        // Create and style the title label
        titleLabel = new JLabel("Hello World Design");
        titleLabel.setFont(new Font("Arial", Font.BOLD, 24));
        titleLabel.setHorizontalAlignment(JLabel.CENTER);
        titleLabel.setForeground(new Color(0, 102, 204)); // Blue color
        
        // Create and style the message label
        messageLabel = new JLabel("Welcome to my first Java Swing application!");
        messageLabel.setFont(new Font("Arial", Font.PLAIN, 16));
        messageLabel.setHorizontalAlignment(JLabel.CENTER);
        messageLabel.setBorder(BorderFactory.createEmptyBorder(30, 0, 30, 0));
        
        // Create and style the button
        clickMeButton = new JButton("Click Me!");
        clickMeButton.setFont(new Font("Arial", Font.BOLD, 14));
        clickMeButton.setBackground(new Color(240, 240, 240));
        clickMeButton.setFocusPainted(false);
        
        // Add action listener to the button
        clickMeButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                messageLabel.setText("Hello, World! Button was clicked!");
                messageLabel.setForeground(new Color(0, 153, 0)); // Green color
            }
        });
        
        // Add components to panels
        buttonPanel.add(clickMeButton);
        
        // Add panels to main panel
        mainPanel.add(titleLabel, BorderLayout.NORTH);
        mainPanel.add(messageLabel, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        // Set background colors
        mainPanel.setBackground(new Color(245, 245, 250)); // Light blue-gray
        buttonPanel.setBackground(new Color(245, 245, 250)); // Match main panel
    }
    
    /**
     * Main method to test the HelloWorldPage directly
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        // Use the Event Dispatch Thread for Swing applications
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new HelloWorldPage();
            }
        });
    }
}
