package test1;

import javax.swing.*;
import javax.swing.border.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;

/**
 * Modern, professional hotel guest registration interface
 * Features: Gradient backgrounds, rounded corners, modern colors, better UX
 */
public class ModernRegisterPage extends J<PERSON>rame {
    // Modern color scheme
    private static final Color PRIMARY_COLOR = new Color(41, 128, 185);      // Professional blue
    private static final Color SECONDARY_COLOR = new Color(52, 152, 219);    // Lighter blue
    private static final Color ACCENT_COLOR = new Color(46, 204, 113);       // Green accent
    private static final Color DANGER_COLOR = new Color(231, 76, 60);        // Red for warnings
    private static final Color BACKGROUND_COLOR = new Color(236, 240, 241);  // Light gray background
    private static final Color CARD_COLOR = Color.WHITE;                     // White cards
    private static final Color TEXT_COLOR = new Color(44, 62, 80);           // Dark text
    private static final Color PLACEHOLDER_COLOR = new Color(149, 165, 166); // Gray placeholder
    
    // UI Components
    private JTextField usernameField;
    private JTextField firstNameField;
    private JTextField lastNameField;
    private JTextField icField;
    private JButton registerButton;
    private JButton viewUsersButton;
    private JButton backToHotelButton;
    private JLabel statusLabel;

    // Database manager instance
    private NetBeansDatabaseManager dbManager;

    public ModernRegisterPage() {
        // Initialize database manager
        dbManager = NetBeansDatabaseManager.getInstance();

        // Modern window setup
        setTitle("🏨 Luxury Hotel Management System");
        setSize(900, 750);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setResizable(false);

        // Set modern look and feel
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            // Use default if system L&F not available
        }

        // Create main panel with gradient background
        JPanel mainPanel = createMainPanel();
        add(mainPanel);

        setVisible(true);
    }

    /**
     * Create the main panel with modern design
     */
    private JPanel createMainPanel() {
        JPanel mainPanel = new JPanel() {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                
                // Create gradient background
                GradientPaint gradient = new GradientPaint(
                    0, 0, BACKGROUND_COLOR,
                    0, getHeight(), new Color(189, 195, 199)
                );
                g2d.setPaint(gradient);
                g2d.fillRect(0, 0, getWidth(), getHeight());
            }
        };
        mainPanel.setLayout(new BorderLayout());

        // Add components
        mainPanel.add(createHeaderPanel(), BorderLayout.NORTH);
        mainPanel.add(createCenterPanel(), BorderLayout.CENTER);
        mainPanel.add(createFooterPanel(), BorderLayout.SOUTH);

        return mainPanel;
    }

    /**
     * Create modern header with hotel branding
     */
    private JPanel createHeaderPanel() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setOpaque(false);
        headerPanel.setBorder(BorderFactory.createEmptyBorder(30, 40, 20, 40));

        // Main title
        JLabel titleLabel = new JLabel("🏨 Guest Registration", JLabel.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 32));
        titleLabel.setForeground(PRIMARY_COLOR);

        // Subtitle
        JLabel subtitleLabel = new JLabel("Welcome to Luxury Hotel Management System", JLabel.CENTER);
        subtitleLabel.setFont(new Font("Segoe UI", Font.PLAIN, 16));
        subtitleLabel.setForeground(TEXT_COLOR);

        // Status label for feedback
        statusLabel = new JLabel(" ", JLabel.CENTER);
        statusLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));

        JPanel titlePanel = new JPanel(new GridLayout(3, 1, 0, 5));
        titlePanel.setOpaque(false);
        titlePanel.add(titleLabel);
        titlePanel.add(subtitleLabel);
        titlePanel.add(statusLabel);

        headerPanel.add(titlePanel, BorderLayout.CENTER);
        return headerPanel;
    }

    /**
     * Create center panel with registration form
     */
    private JPanel createCenterPanel() {
        JPanel centerPanel = new JPanel(new GridBagLayout());
        centerPanel.setOpaque(false);
        centerPanel.setBorder(BorderFactory.createEmptyBorder(20, 40, 20, 40));

        // Create form card
        JPanel formCard = createFormCard();
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;
        
        centerPanel.add(formCard, gbc);
        return centerPanel;
    }

    /**
     * Create the registration form card
     */
    private JPanel createFormCard() {
        JPanel card = new JPanel();
        card.setLayout(new BorderLayout());
        card.setBackground(CARD_COLOR);
        card.setBorder(createRoundedBorder());

        // Form content
        JPanel formContent = new JPanel(new GridBagLayout());
        formContent.setBackground(CARD_COLOR);
        formContent.setBorder(BorderFactory.createEmptyBorder(40, 40, 40, 40));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.WEST;

        // Form title
        JLabel formTitle = new JLabel("Guest Information");
        formTitle.setFont(new Font("Segoe UI", Font.BOLD, 20));
        formTitle.setForeground(PRIMARY_COLOR);
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        formContent.add(formTitle, gbc);

        // Username field
        gbc.gridwidth = 1; gbc.gridy++;
        formContent.add(createLabel("Username:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        usernameField = createModernTextField("Enter username");
        formContent.add(usernameField, gbc);

        // First name field
        gbc.gridx = 0; gbc.gridy++; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        formContent.add(createLabel("First Name:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        firstNameField = createModernTextField("Enter first name");
        formContent.add(firstNameField, gbc);

        // Last name field
        gbc.gridx = 0; gbc.gridy++; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        formContent.add(createLabel("Last Name:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        lastNameField = createModernTextField("Enter last name");
        formContent.add(lastNameField, gbc);

        // IC field
        gbc.gridx = 0; gbc.gridy++; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        formContent.add(createLabel("IC/Passport:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        icField = createModernTextField("Enter IC or passport number");
        formContent.add(icField, gbc);

        card.add(formContent, BorderLayout.CENTER);
        return card;
    }

    /**
     * Create modern styled label
     */
    private JLabel createLabel(String text) {
        JLabel label = new JLabel(text);
        label.setFont(new Font("Segoe UI", Font.BOLD, 14));
        label.setForeground(TEXT_COLOR);
        return label;
    }

    /**
     * Create modern styled text field with placeholder
     */
    private JTextField createModernTextField(String placeholder) {
        JTextField field = new JTextField(20);
        field.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        field.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(189, 195, 199), 2),
            BorderFactory.createEmptyBorder(10, 15, 10, 15)
        ));
        field.setBackground(Color.WHITE);
        field.setForeground(TEXT_COLOR);
        
        // Add placeholder functionality
        addPlaceholder(field, placeholder);
        
        return field;
    }

    /**
     * Add placeholder text to field
     */
    private void addPlaceholder(JTextField field, String placeholder) {
        field.setText(placeholder);
        field.setForeground(PLACEHOLDER_COLOR);
        
        field.addFocusListener(new FocusListener() {
            @Override
            public void focusGained(FocusEvent e) {
                if (field.getText().equals(placeholder)) {
                    field.setText("");
                    field.setForeground(TEXT_COLOR);
                }
                field.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(PRIMARY_COLOR, 2),
                    BorderFactory.createEmptyBorder(10, 15, 10, 15)
                ));
            }

            @Override
            public void focusLost(FocusEvent e) {
                if (field.getText().isEmpty()) {
                    field.setText(placeholder);
                    field.setForeground(PLACEHOLDER_COLOR);
                }
                field.setBorder(BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(new Color(189, 195, 199), 2),
                    BorderFactory.createEmptyBorder(10, 15, 10, 15)
                ));
            }
        });
    }

    /**
     * Create footer panel with action buttons
     */
    private JPanel createFooterPanel() {
        JPanel footerPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 20, 20));
        footerPanel.setOpaque(false);
        footerPanel.setBorder(BorderFactory.createEmptyBorder(20, 40, 30, 40));

        // Register button
        registerButton = createModernButton("Register Guest", PRIMARY_COLOR, Color.WHITE);
        registerButton.addActionListener(e -> performRegistration());

        // View users button
        viewUsersButton = createModernButton("View All Guests", ACCENT_COLOR, Color.WHITE);
        viewUsersButton.addActionListener(e -> {
            System.out.println("\n=== VIEW ALL USERS CLICKED ===");
            dbManager.showAllUsersDebug();
            showAllUsers();
        });

        // Back button
        backToHotelButton = createModernButton("Back to Hotel", SECONDARY_COLOR, Color.WHITE);
        backToHotelButton.addActionListener(e -> {
            dispose();
            new HotelManagementSystem();
        });

        footerPanel.add(registerButton);
        footerPanel.add(viewUsersButton);
        footerPanel.add(backToHotelButton);

        return footerPanel;
    }

    /**
     * Create modern styled button
     */
    private JButton createModernButton(String text, Color bgColor, Color textColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("Segoe UI", Font.BOLD, 14));
        button.setBackground(bgColor);
        button.setForeground(textColor);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setPreferredSize(new Dimension(160, 45));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // Add hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(bgColor.darker());
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(bgColor);
            }
        });
        
        return button;
    }

    /**
     * Create rounded border for cards
     */
    private Border createRoundedBorder() {
        return BorderFactory.createCompoundBorder(
            new RoundedBorder(15),
            BorderFactory.createEmptyBorder(5, 5, 5, 5)
        );
    }

    /**
     * Custom rounded border class
     */
    private static class RoundedBorder implements Border {
        private int radius;

        RoundedBorder(int radius) {
            this.radius = radius;
        }

        public Insets getBorderInsets(Component c) {
            return new Insets(this.radius + 1, this.radius + 1, this.radius + 2, this.radius);
        }

        public boolean isBorderOpaque() {
            return true;
        }

        public void paintBorder(Component c, Graphics g, int x, int y, int width, int height) {
            Graphics2D g2d = (Graphics2D) g;
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setColor(new Color(189, 195, 199));
            g2d.drawRoundRect(x, y, width - 1, height - 1, radius, radius);
        }
    }

    /**
     * Get field value, handling placeholder text
     */
    private String getFieldValue(JTextField field, String placeholder) {
        String value = field.getText().trim();
        return value.equals(placeholder) ? "" : value;
    }

    /**
     * Perform user registration with modern feedback
     */
    private void performRegistration() {
        // Get values from fields
        String username = getFieldValue(usernameField, "Enter username");
        String firstName = getFieldValue(firstNameField, "Enter first name");
        String lastName = getFieldValue(lastNameField, "Enter last name");
        String ic = getFieldValue(icField, "Enter IC or passport number");

        // Validate input
        if (username.isEmpty() || firstName.isEmpty() || lastName.isEmpty() || ic.isEmpty()) {
            showStatus("❌ Please fill in all fields", DANGER_COLOR);
            return;
        }

        // Show loading status
        showStatus("⏳ Registering guest...", PRIMARY_COLOR);
        registerButton.setEnabled(false);

        // Simulate processing delay for better UX
        Timer timer = new Timer(1000, e -> {
            // Try to register user in database
            boolean success = dbManager.registerUser(username, firstName, lastName, ic);

            if (success) {
                showStatus("✅ Registration successful!", ACCENT_COLOR);

                // Show success dialog with modern styling
                showSuccessDialog(username, firstName, lastName, ic);

                // Clear form
                clearForm();
            } else {
                showStatus("❌ Registration failed. Please try again.", DANGER_COLOR);
            }

            registerButton.setEnabled(true);
        });
        timer.setRepeats(false);
        timer.start();
    }

    /**
     * Show status message with color
     */
    private void showStatus(String message, Color color) {
        statusLabel.setText(message);
        statusLabel.setForeground(color);
    }

    /**
     * Show modern success dialog
     */
    private void showSuccessDialog(String username, String firstName, String lastName, String ic) {
        JDialog dialog = new JDialog(this, "Registration Successful", true);
        dialog.setSize(400, 300);
        dialog.setLocationRelativeTo(this);
        dialog.setResizable(false);

        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(CARD_COLOR);
        panel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        // Success icon and title
        JLabel iconLabel = new JLabel("✅", JLabel.CENTER);
        iconLabel.setFont(new Font("Segoe UI", Font.PLAIN, 48));

        JLabel titleLabel = new JLabel("Registration Successful!", JLabel.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 18));
        titleLabel.setForeground(ACCENT_COLOR);

        // Guest details
        String details = String.format(
            "<html><div style='text-align: center; font-family: Segoe UI;'>" +
            "<b>Guest Details:</b><br><br>" +
            "<b>Username:</b> %s<br>" +
            "<b>Name:</b> %s %s<br>" +
            "<b>IC/Passport:</b> %s<br><br>" +
            "<i>Guest has been added to the hotel database.</i>" +
            "</div></html>",
            username, firstName, lastName, ic
        );

        JLabel detailsLabel = new JLabel(details, JLabel.CENTER);
        detailsLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        detailsLabel.setForeground(TEXT_COLOR);

        // OK button
        JButton okButton = createModernButton("OK", PRIMARY_COLOR, Color.WHITE);
        okButton.addActionListener(e -> dialog.dispose());

        JPanel topPanel = new JPanel(new GridLayout(2, 1, 0, 10));
        topPanel.setOpaque(false);
        topPanel.add(iconLabel);
        topPanel.add(titleLabel);

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setOpaque(false);
        buttonPanel.add(okButton);

        panel.add(topPanel, BorderLayout.NORTH);
        panel.add(detailsLabel, BorderLayout.CENTER);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        dialog.add(panel);
        dialog.setVisible(true);
    }

    /**
     * Clear form fields
     */
    private void clearForm() {
        usernameField.setText("Enter username");
        usernameField.setForeground(PLACEHOLDER_COLOR);
        firstNameField.setText("Enter first name");
        firstNameField.setForeground(PLACEHOLDER_COLOR);
        lastNameField.setText("Enter last name");
        lastNameField.setForeground(PLACEHOLDER_COLOR);
        icField.setText("Enter IC or passport number");
        icField.setForeground(PLACEHOLDER_COLOR);
    }

    /**
     * Show all users in a modern dialog
     */
    private void showAllUsers() {
        java.util.List<String[]> users = dbManager.getAllUsers();

        JDialog dialog = new JDialog(this, "All Registered Guests", true);
        dialog.setSize(700, 500);
        dialog.setLocationRelativeTo(this);

        // Create table with modern styling
        String[] columnNames = {"Username", "First Name", "Last Name", "IC/Passport", "Info"};
        String[][] data = new String[users.size()][5];

        for (int i = 0; i < users.size(); i++) {
            data[i] = users.get(i);
        }

        JTable table = new JTable(data, columnNames);
        table.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        table.setRowHeight(30);
        table.setGridColor(new Color(189, 195, 199));
        table.setSelectionBackground(SECONDARY_COLOR);
        table.setSelectionForeground(Color.WHITE);

        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());

        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        panel.add(scrollPane, BorderLayout.CENTER);

        // Close button
        JButton closeButton = createModernButton("Close", PRIMARY_COLOR, Color.WHITE);
        closeButton.addActionListener(e -> dialog.dispose());

        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.add(closeButton);
        panel.add(buttonPanel, BorderLayout.SOUTH);

        dialog.add(panel);
        dialog.setVisible(true);
    }

    /**
     * Main method to run the modern registration page
     */
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            } catch (Exception e) {
                e.printStackTrace();
            }
            new ModernRegisterPage();
        });
    }
}
