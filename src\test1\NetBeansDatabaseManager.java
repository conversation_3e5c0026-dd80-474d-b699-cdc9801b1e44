/*
 * Database Manager using NetBeans approach
 */
package test1;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Database Manager that works with NetBeans Derby setup
 * 
 * <AUTHOR>
 */
public class NetBeansDatabaseManager {
    
    // Singleton instance
    private static NetBeansDatabaseManager instance;
    private Connection connection;
    
    /**
     * Private constructor for singleton pattern
     */
    private NetBeansDatabaseManager() {
        establishConnection();
    }
    
    /**
     * Get singleton instance
     */
    public static synchronized NetBeansDatabaseManager getInstance() {
        if (instance == null) {
            instance = new NetBeansDatabaseManager();
        }
        return instance;
    }
    
    /**
     * Establish database connection using multiple approaches
     */
    private void establishConnection() {
        // Try different approaches to connect
        
        // Approach 1: Try with explicit driver loading
        if (tryWithDriverLoading()) return;
        
        // Approach 2: Try with embedded database
        if (tryEmbeddedDatabase()) return;
        
        // Approach 3: Create in-memory database for testing
        createInMemoryDatabase();
    }
    
    /**
     * Try connecting with explicit driver loading
     */
    private boolean tryWithDriverLoading() {
        try {
            System.out.println("Attempting to load Derby client driver...");
            
            // Try to load the Derby client driver
            Class.forName("org.apache.derby.jdbc.ClientDriver");
            System.out.println("✓ Derby client driver loaded successfully!");
            
            // Try to connect
            String url = "*************************************";
            connection = DriverManager.getConnection(url);

            // Set auto-commit to false for better transaction control
            connection.setAutoCommit(false);

            System.out.println("✓ Connected to Derby server successfully!");
            System.out.println("  Database URL: " + url);
            System.out.println("  Connection: " + connection.toString());

            createHotelTables();
            return true;
            
        } catch (ClassNotFoundException e) {
            System.out.println("Derby client driver not found, trying embedded...");
            return false;
        } catch (SQLException e) {
            System.out.println("Cannot connect to Derby server: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Try embedded database
     */
    private boolean tryEmbeddedDatabase() {
        try {
            System.out.println("Attempting embedded Derby database...");
            
            // Try to load embedded driver
            Class.forName("org.apache.derby.jdbc.EmbeddedDriver");
            System.out.println("✓ Derby embedded driver loaded!");
            
            // Create embedded database
            String url = "******************************";
            connection = DriverManager.getConnection(url);

            // Set auto-commit to false for better transaction control
            connection.setAutoCommit(false);

            System.out.println("✓ Connected to embedded Derby database!");
            System.out.println("  Database URL: " + url);
            System.out.println("  Connection: " + connection.toString());

            createHotelTables();
            return true;
            
        } catch (ClassNotFoundException e) {
            System.out.println("Derby embedded driver not found, using in-memory...");
            return false;
        } catch (SQLException e) {
            System.out.println("Cannot create embedded database: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Create in-memory database for testing
     */
    private void createInMemoryDatabase() {
        try {
            System.out.println("Creating in-memory database for testing...");
            
            // Use H2 in-memory database if available, or create mock data
            connection = null; // Will use mock data
            System.out.println("✓ Using mock database for testing");
            
        } catch (Exception e) {
            System.err.println("Error creating in-memory database: " + e.getMessage());
        }
    }
    
    /**
     * Get database connection
     */
    public Connection getConnection() {
        return connection;
    }
    
    /**
     * Create hotel tables
     */
    private void createHotelTables() {
        if (connection == null) return;
        
        try {
            Statement stmt = connection.createStatement();
            
            // Create Users table
            String createUsersTable = "CREATE TABLE HOTEL_USERS (" +
                "id INTEGER NOT NULL GENERATED ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1), " +
                "username VARCHAR(50) NOT NULL, " +
                "firstName VARCHAR(50) NOT NULL, " +
                "lastName VARCHAR(50) NOT NULL, " +
                "icNumber VARCHAR(20) NOT NULL, " +
                "registrationDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "PRIMARY KEY (id)" +
                ")";
            
            // Create Rooms table
            String createRoomsTable = "CREATE TABLE HOTEL_ROOMS (" +
                "roomNumber INTEGER NOT NULL, " +
                "roomType VARCHAR(20) NOT NULL, " +
                "price DECIMAL(10,2) NOT NULL, " +
                "status VARCHAR(20) DEFAULT 'AVAILABLE', " +
                "description VARCHAR(200), " +
                "PRIMARY KEY (roomNumber)" +
                ")";
            
            // Try to create tables
            try {
                stmt.executeUpdate(createUsersTable);
                System.out.println("✓ HOTEL_USERS table created!");
            } catch (SQLException e) {
                if (e.getSQLState().equals("X0Y32")) {
                    System.out.println("HOTEL_USERS table already exists.");
                }
            }
            
            try {
                stmt.executeUpdate(createRoomsTable);
                System.out.println("✓ HOTEL_ROOMS table created!");
                insertSampleRooms();
            } catch (SQLException e) {
                if (e.getSQLState().equals("X0Y32")) {
                    System.out.println("HOTEL_ROOMS table already exists.");
                }
            }
            
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error creating tables: " + e.getMessage());
        }
    }
    
    /**
     * Insert sample rooms
     */
    private void insertSampleRooms() {
        if (connection == null) return;
        
        try {
            String insertRoom = "INSERT INTO HOTEL_ROOMS (roomNumber, roomType, price, status, description) VALUES (?, ?, ?, ?, ?)";
            PreparedStatement pstmt = connection.prepareStatement(insertRoom);
            
            Object[][] rooms = {
                {101, "SINGLE", 80.00, "AVAILABLE", "Standard single room"},
                {102, "SINGLE", 80.00, "OCCUPIED", "Standard single room"},
                {201, "DOUBLE", 120.00, "AVAILABLE", "Double room"},
                {202, "DOUBLE", 120.00, "AVAILABLE", "Double room"}
            };
            
            for (Object[] room : rooms) {
                pstmt.setInt(1, (Integer) room[0]);
                pstmt.setString(2, (String) room[1]);
                pstmt.setDouble(3, (Double) room[2]);
                pstmt.setString(4, (String) room[3]);
                pstmt.setString(5, (String) room[4]);
                
                try {
                    pstmt.executeUpdate();
                } catch (SQLException e) {
                    // Ignore duplicates
                }
            }
            
            pstmt.close();
            System.out.println("✓ Sample rooms inserted!");
            
        } catch (SQLException e) {
            System.err.println("Error inserting rooms: " + e.getMessage());
        }
    }
    
    /**
     * Register a new user (with mock data if no database)
     */
    public boolean registerUser(String username, String firstName, String lastName, String icNumber) {
        if (connection == null) {
            // Mock successful registration
            System.out.println("Mock: Registered user " + username + " (" + firstName + " " + lastName + ")");
            return true;
        }

        try {
            String sql = "INSERT INTO HOTEL_USERS (username, firstName, lastName, icNumber) VALUES (?, ?, ?, ?)";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, firstName);
            pstmt.setString(3, lastName);
            pstmt.setString(4, icNumber);

            int rowsAffected = pstmt.executeUpdate();

            // Commit the transaction explicitly
            connection.commit();

            System.out.println("✓ User registered successfully in database!");
            System.out.println("  Username: " + username);
            System.out.println("  Name: " + firstName + " " + lastName);
            System.out.println("  IC: " + icNumber);
            System.out.println("  Rows affected: " + rowsAffected);

            pstmt.close();

            // Verify the data was inserted by counting records
            verifyUserCount();

            return rowsAffected > 0;

        } catch (SQLException e) {
            System.err.println("Error registering user: " + e.getMessage());
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                System.err.println("Error rolling back: " + rollbackEx.getMessage());
            }
            return false;
        }
    }

    /**
     * Verify user count after insertion
     */
    private void verifyUserCount() {
        try {
            String countSql = "SELECT COUNT(*) as total FROM HOTEL_USERS";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(countSql);

            if (rs.next()) {
                int total = rs.getInt("total");
                System.out.println("✓ Total users in database: " + total);
            }

            rs.close();
            stmt.close();

        } catch (SQLException e) {
            System.err.println("Error verifying user count: " + e.getMessage());
        }
    }
    
    /**
     * Get all users (with mock data if no database)
     */
    public List<String[]> getAllUsers() {
        List<String[]> users = new ArrayList<>();
        
        if (connection == null) {
            // Return mock data
            users.add(new String[]{"john_doe", "John", "Doe", "123456789", "2024-01-15 10:30:00"});
            users.add(new String[]{"jane_smith", "Jane", "Smith", "987654321", "2024-01-16 14:20:00"});
            return users;
        }
        
        try {
            String sql = "SELECT username, firstName, lastName, icNumber, registrationDate FROM HOTEL_USERS ORDER BY registrationDate DESC";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                String[] user = {
                    rs.getString("username"),
                    rs.getString("firstName"),
                    rs.getString("lastName"),
                    rs.getString("icNumber"),
                    rs.getTimestamp("registrationDate").toString()
                };
                users.add(user);
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error getting users: " + e.getMessage());
        }
        
        return users;
    }
    
    /**
     * Get room statistics (with mock data if no database)
     */
    public int[] getRoomStatistics() {
        if (connection == null) {
            // Return mock statistics
            return new int[]{4, 1, 3, 0}; // total, occupied, available, maintenance
        }
        
        int[] stats = new int[4];
        try {
            String sql = "SELECT status, COUNT(*) as count FROM HOTEL_ROOMS GROUP BY status";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                String status = rs.getString("status");
                int count = rs.getInt("count");
                
                switch (status.toUpperCase()) {
                    case "OCCUPIED": stats[1] = count; break;
                    case "AVAILABLE": stats[2] = count; break;
                    case "MAINTENANCE": stats[3] = count; break;
                }
            }
            
            stats[0] = stats[1] + stats[2] + stats[3];
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error getting room statistics: " + e.getMessage());
            return new int[]{4, 1, 3, 0};
        }
        
        return stats;
    }
    
    /**
     * Get total guests (with mock data if no database)
     */
    public int getTotalGuests() {
        if (connection == null) {
            return 2; // Mock data
        }
        
        try {
            String sql = "SELECT COUNT(*) as total FROM HOTEL_USERS";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            if (rs.next()) {
                int total = rs.getInt("total");
                rs.close();
                stmt.close();
                return total;
            }
            
        } catch (SQLException e) {
            System.err.println("Error getting guest count: " + e.getMessage());
        }
        
        return 0;
    }

    /**
     * Debug method to show all users in the database
     */
    public void showAllUsersDebug() {
        if (connection == null) {
            System.out.println("No database connection - using mock data");
            return;
        }

        try {
            String sql = "SELECT * FROM HOTEL_USERS ORDER BY registrationDate DESC";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            System.out.println("\n=== ALL USERS IN DATABASE ===");
            int count = 0;
            while (rs.next()) {
                count++;
                System.out.println("User " + count + ":");
                System.out.println("  ID: " + rs.getInt("id"));
                System.out.println("  Username: " + rs.getString("username"));
                System.out.println("  Name: " + rs.getString("firstName") + " " + rs.getString("lastName"));
                System.out.println("  IC: " + rs.getString("icNumber"));
                System.out.println("  Date: " + rs.getTimestamp("registrationDate"));
                System.out.println();
            }

            if (count == 0) {
                System.out.println("No users found in HOTEL_USERS table");
            } else {
                System.out.println("Total users found: " + count);
            }
            System.out.println("==============================\n");

            rs.close();
            stmt.close();

        } catch (SQLException e) {
            System.err.println("Error showing users: " + e.getMessage());
        }
    }
}
