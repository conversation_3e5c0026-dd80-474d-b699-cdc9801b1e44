/*
 * Database Manager using NetBeans approach
 */
package test1;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Database Manager that works with NetBeans Derby setup
 * 
 * <AUTHOR>
 */
public class NetBeansDatabaseManager {
    
    // Singleton instance
    private static NetBeansDatabaseManager instance;
    private Connection connection;
    
    /**
     * Private constructor for singleton pattern
     */
    private NetBeansDatabaseManager() {
        establishConnection();
    }
    
    /**
     * Get singleton instance
     */
    public static synchronized NetBeansDatabaseManager getInstance() {
        if (instance == null) {
            instance = new NetBeansDatabaseManager();
        }
        return instance;
    }
    
    /**
     * Establish database connection using multiple approaches
     */
    private void establishConnection() {
        // Try different approaches to connect
        
        // Approach 1: Try with explicit driver loading
        if (tryWithDriverLoading()) return;
        
        // Approach 2: Try with embedded database
        if (tryEmbeddedDatabase()) return;
        
        // Approach 3: Create in-memory database for testing
        createInMemoryDatabase();
    }
    
    /**
     * Try connecting with explicit driver loading
     */
    private boolean tryWithDriverLoading() {
        // Try multiple approaches to connect to your PayrollDB

        // Approach 1: Try with driver loading
        try {
            System.out.println("Attempting to load Derby client driver...");
            Class.forName("org.apache.derby.jdbc.ClientDriver");
            System.out.println("✓ Derby client driver loaded successfully!");
        } catch (ClassNotFoundException e) {
            System.out.println("Derby client driver not found, trying direct connection...");
        }

        // Approach 2: Try direct connection (sometimes works without explicit driver loading)
        try {
            String url = "*************************************";
            System.out.println("Attempting connection to: " + url);
            connection = DriverManager.getConnection(url);

            // Set auto-commit to true for immediate commits
            connection.setAutoCommit(true);

            System.out.println("✓ Connected to Derby server successfully!");
            System.out.println("  Database URL: " + url);
            System.out.println("  Connection: " + connection.toString());

            createHotelTables();
            return true;

        } catch (SQLException e) {
            System.out.println("Cannot connect to Derby server: " + e.getMessage());
            System.out.println("  SQL State: " + e.getSQLState());
            System.out.println("  Error Code: " + e.getErrorCode());

            // Try with different URL format
            try {
                String altUrl = "*****************************************";
                System.out.println("Trying alternative URL: " + altUrl);
                connection = DriverManager.getConnection(altUrl);
                connection.setAutoCommit(true);

                System.out.println("✓ Connected with alternative URL!");
                createHotelTables();
                return true;

            } catch (SQLException e2) {
                System.out.println("Alternative URL also failed: " + e2.getMessage());
                return false;
            }
        }
    }
    
    /**
     * Try embedded database
     */
    private boolean tryEmbeddedDatabase() {
        try {
            System.out.println("Attempting embedded Derby database...");
            
            // Try to load embedded driver
            Class.forName("org.apache.derby.jdbc.EmbeddedDriver");
            System.out.println("✓ Derby embedded driver loaded!");
            
            // Create embedded database
            String url = "******************************";
            connection = DriverManager.getConnection(url);

            // Set auto-commit to true for immediate commits
            connection.setAutoCommit(true);

            System.out.println("✓ Connected to embedded Derby database!");
            System.out.println("  Database URL: " + url);
            System.out.println("  Connection: " + connection.toString());

            createHotelTables();
            return true;
            
        } catch (ClassNotFoundException e) {
            System.out.println("Derby embedded driver not found, using in-memory...");
            return false;
        } catch (SQLException e) {
            System.out.println("Cannot create embedded database: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Create in-memory database for testing
     */
    private void createInMemoryDatabase() {
        try {
            System.out.println("Creating in-memory database for testing...");
            
            // Use H2 in-memory database if available, or create mock data
            connection = null; // Will use mock data
            System.out.println("✓ Using mock database for testing");
            
        } catch (Exception e) {
            System.err.println("Error creating in-memory database: " + e.getMessage());
        }
    }
    
    /**
     * Get database connection
     */
    public Connection getConnection() {
        return connection;
    }
    
    /**
     * Create hotel tables and check existing EMPLOYEES table
     */
    private void createHotelTables() {
        if (connection == null) return;

        try {
            Statement stmt = connection.createStatement();

            // Check if EMPLOYEES table exists and show its structure
            checkEmployeesTable();

            // Create Rooms table for hotel management
            String createRoomsTable = "CREATE TABLE HOTEL_ROOMS (" +
                "roomNumber INTEGER NOT NULL, " +
                "roomType VARCHAR(20) NOT NULL, " +
                "price DECIMAL(10,2) NOT NULL, " +
                "status VARCHAR(20) DEFAULT 'AVAILABLE', " +
                "description VARCHAR(200), " +
                "PRIMARY KEY (roomNumber)" +
                ")";

            // Try to create rooms table
            try {
                stmt.executeUpdate(createRoomsTable);
                System.out.println("✓ HOTEL_ROOMS table created!");
                insertSampleRooms();
            } catch (SQLException e) {
                if (e.getSQLState().equals("X0Y32")) {
                    System.out.println("HOTEL_ROOMS table already exists.");
                } else {
                    System.err.println("Error creating HOTEL_ROOMS table: " + e.getMessage());
                }
            }

            stmt.close();

        } catch (SQLException e) {
            System.err.println("Error creating tables: " + e.getMessage());
        }
    }

    /**
     * Check and display EMPLOYEES table structure
     */
    private void checkEmployeesTable() {
        try {
            // Check if EMPLOYEES table exists
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tables = metaData.getTables(null, "APP", "EMPLOYEES", null);

            if (tables.next()) {
                System.out.println("✓ Found existing EMPLOYEES table!");

                // Show current data count
                Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as total FROM EMPLOYEES");
                if (rs.next()) {
                    int count = rs.getInt("total");
                    System.out.println("  Current records in EMPLOYEES: " + count);
                }
                rs.close();
                stmt.close();

            } else {
                System.out.println("EMPLOYEES table not found.");
            }

            tables.close();

        } catch (SQLException e) {
            System.err.println("Error checking EMPLOYEES table: " + e.getMessage());
        }
    }
    
    /**
     * Insert sample rooms
     */
    private void insertSampleRooms() {
        if (connection == null) return;
        
        try {
            String insertRoom = "INSERT INTO HOTEL_ROOMS (roomNumber, roomType, price, status, description) VALUES (?, ?, ?, ?, ?)";
            PreparedStatement pstmt = connection.prepareStatement(insertRoom);
            
            Object[][] rooms = {
                {101, "SINGLE", 80.00, "AVAILABLE", "Standard single room"},
                {102, "SINGLE", 80.00, "OCCUPIED", "Standard single room"},
                {201, "DOUBLE", 120.00, "AVAILABLE", "Double room"},
                {202, "DOUBLE", 120.00, "AVAILABLE", "Double room"}
            };
            
            for (Object[] room : rooms) {
                pstmt.setInt(1, (Integer) room[0]);
                pstmt.setString(2, (String) room[1]);
                pstmt.setDouble(3, (Double) room[2]);
                pstmt.setString(4, (String) room[3]);
                pstmt.setString(5, (String) room[4]);
                
                try {
                    pstmt.executeUpdate();
                } catch (SQLException e) {
                    // Ignore duplicates
                }
            }
            
            pstmt.close();
            System.out.println("✓ Sample rooms inserted!");
            
        } catch (SQLException e) {
            System.err.println("Error inserting rooms: " + e.getMessage());
        }
    }
    
    /**
     * Register a new user in the existing EMPLOYEES table
     */
    public boolean registerUser(String username, String firstName, String lastName, String icNumber) {
        if (connection == null) {
            // Mock successful registration
            System.out.println("Mock: Registered user " + username + " (" + firstName + " " + lastName + ")");
            return true;
        }

        try {
            // Use your existing EMPLOYEES table structure
            String sql = "INSERT INTO EMPLOYEES (USERNAME, FIRST_NAME, LAST_NAME, IC_PASSPORT) VALUES (?, ?, ?, ?)";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, firstName);
            pstmt.setString(3, lastName);
            pstmt.setString(4, icNumber);

            System.out.println("Inserting into EMPLOYEES table:");
            System.out.println("  USERNAME: " + username);
            System.out.println("  FIRST_NAME: " + firstName);
            System.out.println("  LAST_NAME: " + lastName);
            System.out.println("  IC_PASSPORT: " + icNumber);

            int rowsAffected = pstmt.executeUpdate();

            System.out.println("✓ Employee registered successfully in EMPLOYEES table!");
            System.out.println("  Rows affected: " + rowsAffected);

            pstmt.close();

            // Verify the data was inserted by counting records
            verifyUserCount();

            return rowsAffected > 0;

        } catch (SQLException e) {
            System.err.println("Error registering employee: " + e.getMessage());
            System.err.println("SQL State: " + e.getSQLState());
            System.err.println("Error Code: " + e.getErrorCode());
            return false;
        }
    }

    /**
     * Verify employee count after insertion
     */
    private void verifyUserCount() {
        try {
            String countSql = "SELECT COUNT(*) as total FROM EMPLOYEES";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(countSql);

            if (rs.next()) {
                int total = rs.getInt("total");
                System.out.println("✓ Total employees in database: " + total);
            }

            rs.close();
            stmt.close();

        } catch (SQLException e) {
            System.err.println("Error verifying employee count: " + e.getMessage());
        }
    }
    
    /**
     * Get all employees from EMPLOYEES table
     */
    public List<String[]> getAllUsers() {
        List<String[]> users = new ArrayList<>();

        if (connection == null) {
            // Return mock data
            users.add(new String[]{"john_doe", "John", "Doe", "*********", "N/A"});
            users.add(new String[]{"jane_smith", "Jane", "Smith", "*********", "N/A"});
            return users;
        }

        try {
            String sql = "SELECT ID, USERNAME, FIRST_NAME, LAST_NAME, IC_PASSPORT FROM EMPLOYEES ORDER BY ID DESC";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                String[] user = {
                    rs.getString("USERNAME"),
                    rs.getString("FIRST_NAME"),
                    rs.getString("LAST_NAME"),
                    rs.getString("IC_PASSPORT"),
                    "ID: " + rs.getInt("ID") // Show ID instead of date since EMPLOYEES table might not have date
                };
                users.add(user);
            }

            rs.close();
            stmt.close();

        } catch (SQLException e) {
            System.err.println("Error getting employees: " + e.getMessage());
        }

        return users;
    }
    
    /**
     * Get room statistics (with mock data if no database)
     */
    public int[] getRoomStatistics() {
        if (connection == null) {
            // Return mock statistics
            return new int[]{4, 1, 3, 0}; // total, occupied, available, maintenance
        }
        
        int[] stats = new int[4];
        try {
            String sql = "SELECT status, COUNT(*) as count FROM HOTEL_ROOMS GROUP BY status";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                String status = rs.getString("status");
                int count = rs.getInt("count");
                
                switch (status.toUpperCase()) {
                    case "OCCUPIED": stats[1] = count; break;
                    case "AVAILABLE": stats[2] = count; break;
                    case "MAINTENANCE": stats[3] = count; break;
                }
            }
            
            stats[0] = stats[1] + stats[2] + stats[3];
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error getting room statistics: " + e.getMessage());
            return new int[]{4, 1, 3, 0};
        }
        
        return stats;
    }
    
    /**
     * Get total employees (with mock data if no database)
     */
    public int getTotalGuests() {
        if (connection == null) {
            return 2; // Mock data
        }

        try {
            String sql = "SELECT COUNT(*) as total FROM EMPLOYEES";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            if (rs.next()) {
                int total = rs.getInt("total");
                rs.close();
                stmt.close();
                return total;
            }

        } catch (SQLException e) {
            System.err.println("Error getting employee count: " + e.getMessage());
        }

        return 0;
    }

    /**
     * Debug method to show all employees in the database
     */
    public void showAllUsersDebug() {
        if (connection == null) {
            System.out.println("No database connection - using mock data");
            return;
        }

        try {
            String sql = "SELECT * FROM EMPLOYEES ORDER BY ID DESC";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            System.out.println("\n=== ALL EMPLOYEES IN DATABASE ===");
            int count = 0;
            while (rs.next()) {
                count++;
                System.out.println("Employee " + count + ":");
                System.out.println("  ID: " + rs.getInt("ID"));
                System.out.println("  Username: " + rs.getString("USERNAME"));
                System.out.println("  Name: " + rs.getString("FIRST_NAME") + " " + rs.getString("LAST_NAME"));
                System.out.println("  IC/Passport: " + rs.getString("IC_PASSPORT"));
                System.out.println();
            }

            if (count == 0) {
                System.out.println("No employees found in EMPLOYEES table");
            } else {
                System.out.println("Total employees found: " + count);
            }
            System.out.println("===================================\n");

            rs.close();
            stmt.close();

        } catch (SQLException e) {
            System.err.println("Error showing employees: " + e.getMessage());
        }
    }
}
