/*
 * Hotel Management System Main Page
 */
package test1;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Hotel Management System Main Page
 *
 * <AUTHOR>
 */
public class NewJFrame extends javax.swing.JFrame {

    // UI Components
    private JPanel mainPanel;
    private JPanel headerPanel;
    private JPanel sidebarPanel;
    private JPanel contentPanel;
    private JPanel statusPanel;

    private JLabel hotelNameLabel;
    private JLabel dateTimeLabel;
    private JLabel welcomeLabel;
    private JLabel occupancyLabel;
    private JLabel revenueLabel;

    private JButton checkInButton;
    private JButton checkOutButton;
    private JButton roomServiceButton;
    private JButton bookingsButton;
    private JButton guestsButton;
    private JButton reportsButton;
    private JButton settingsButton;

    private Timer dateTimeTimer;

    // Hotel statistics
    private int totalRooms = 8; // Will be updated from database
    private int occupiedRooms = 2; // Will be updated from database
    private double dailyRevenue = 12500.00;
    private DatabaseManager dbManager;

    /**
     * Creates new Hotel Management System
     */
    public NewJFrame() {
        // Initialize database manager
        dbManager = DatabaseManager.getInstance();

        initComponents();
        setupDateTimeTimer();
        setTitle("Luxury Hotel Management System");
        setSize(1000, 700);
        setLocationRelativeTo(null); // Center on screen
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    }

    /**
     * Initialize all components for the Hotel Management System
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {
        // Create main panel with BorderLayout
        mainPanel = new JPanel(new BorderLayout(5, 5));
        mainPanel.setBackground(new Color(245, 245, 250));

        // Create header panel
        createHeaderPanel();

        // Create sidebar panel
        createSidebarPanel();

        // Create content panel
        createContentPanel();

        // Create status panel
        createStatusPanel();

        // Add panels to main panel
        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(sidebarPanel, BorderLayout.WEST);
        mainPanel.add(contentPanel, BorderLayout.CENTER);
        mainPanel.add(statusPanel, BorderLayout.SOUTH);

        // Add main panel to frame
        setContentPane(mainPanel);
    }// </editor-fold>//GEN-END:initComponents

    /**
     * Creates and configures the header panel
     */
    private void createHeaderPanel() {
        headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBackground(new Color(25, 25, 112)); // Midnight Blue
        headerPanel.setPreferredSize(new Dimension(1000, 80));
        headerPanel.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));

        // Hotel name label
        hotelNameLabel = new JLabel("LUXURY HOTEL");
        hotelNameLabel.setFont(new Font("Arial", Font.BOLD, 28));
        hotelNameLabel.setForeground(Color.WHITE);

        // Date and time label
        dateTimeLabel = new JLabel();
        dateTimeLabel.setFont(new Font("Arial", Font.PLAIN, 14));
        dateTimeLabel.setForeground(Color.WHITE);
        dateTimeLabel.setHorizontalAlignment(JLabel.RIGHT);

        // Add components to header panel
        headerPanel.add(hotelNameLabel, BorderLayout.WEST);
        headerPanel.add(dateTimeLabel, BorderLayout.EAST);
    }

    /**
     * Creates and configures the sidebar panel with navigation buttons
     */
    private void createSidebarPanel() {
        sidebarPanel = new JPanel();
        sidebarPanel.setLayout(new BoxLayout(sidebarPanel, BoxLayout.Y_AXIS));
        sidebarPanel.setBackground(new Color(45, 45, 45)); // Dark Gray
        sidebarPanel.setPreferredSize(new Dimension(200, 600));
        sidebarPanel.setBorder(BorderFactory.createEmptyBorder(20, 10, 20, 10));

        // Create navigation buttons
        checkInButton = createNavButton("Check-In");
        checkOutButton = createNavButton("Check-Out");
        roomServiceButton = createNavButton("Room Service");
        bookingsButton = createNavButton("Bookings");
        guestsButton = createNavButton("Guests");
        reportsButton = createNavButton("Reports");
        settingsButton = createNavButton("Settings");

        // Add buttons to sidebar panel with spacing
        sidebarPanel.add(checkInButton);
        sidebarPanel.add(Box.createRigidArea(new Dimension(0, 15)));
        sidebarPanel.add(checkOutButton);
        sidebarPanel.add(Box.createRigidArea(new Dimension(0, 15)));
        sidebarPanel.add(roomServiceButton);
        sidebarPanel.add(Box.createRigidArea(new Dimension(0, 15)));
        sidebarPanel.add(bookingsButton);
        sidebarPanel.add(Box.createRigidArea(new Dimension(0, 15)));
        sidebarPanel.add(guestsButton);
        sidebarPanel.add(Box.createRigidArea(new Dimension(0, 15)));
        sidebarPanel.add(reportsButton);
        sidebarPanel.add(Box.createRigidArea(new Dimension(0, 15)));
        sidebarPanel.add(settingsButton);

        // Add filler to push buttons to the top
        sidebarPanel.add(Box.createVerticalGlue());
    }

    /**
     * Creates a styled navigation button
     *
     * @param text The button text
     * @return A styled JButton
     */
    private JButton createNavButton(String text) {
        JButton button = new JButton(text);
        button.setFont(new Font("Arial", Font.BOLD, 14));
        button.setForeground(Color.WHITE);
        button.setBackground(new Color(70, 70, 70));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setMaximumSize(new Dimension(180, 40));
        button.setAlignmentX(Component.CENTER_ALIGNMENT);

        // Add hover effect
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(100, 100, 100));
            }

            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(new Color(70, 70, 70));
            }
        });

        // Add action listener
        button.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                welcomeLabel.setText("You clicked: " + text);
            }
        });

        return button;
    }

    /**
     * Creates and configures the main content panel
     */
    private void createContentPanel() {
        contentPanel = new JPanel();
        contentPanel.setLayout(new BorderLayout());
        contentPanel.setBackground(new Color(240, 240, 245)); // Light Gray
        contentPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Welcome message
        welcomeLabel = new JLabel("Welcome to Luxury Hotel Management System");
        welcomeLabel.setFont(new Font("Arial", Font.BOLD, 24));
        welcomeLabel.setForeground(new Color(25, 25, 112)); // Midnight Blue

        // Dashboard panel
        JPanel dashboardPanel = createDashboardPanel();

        // Quick access panel
        JPanel quickAccessPanel = createQuickAccessPanel();

        // Add components to content panel
        contentPanel.add(welcomeLabel, BorderLayout.NORTH);
        contentPanel.add(dashboardPanel, BorderLayout.CENTER);
        contentPanel.add(quickAccessPanel, BorderLayout.SOUTH);
    }

    /**
     * Creates a dashboard panel with hotel statistics
     *
     * @return The dashboard panel
     */
    private JPanel createDashboardPanel() {
        JPanel dashboardPanel = new JPanel(new GridLayout(2, 2, 15, 15));
        dashboardPanel.setBackground(new Color(240, 240, 245));
        dashboardPanel.setBorder(BorderFactory.createEmptyBorder(20, 0, 20, 0));

        // Get real data from database
        updateStatistics();

        // Room occupancy panel
        JPanel occupancyPanel = createStatPanel("Room Occupancy",
                occupiedRooms + " / " + totalRooms + " (" + (totalRooms > 0 ? (occupiedRooms * 100 / totalRooms) : 0) + "%)",
                new Color(70, 130, 180)); // Steel Blue

        // Revenue panel
        JPanel revenuePanel = createStatPanel("Daily Revenue",
                "$" + String.format("%,.2f", dailyRevenue),
                new Color(46, 139, 87)); // Sea Green

        // Registered guests panel
        int totalGuests = dbManager.getTotalGuests();
        JPanel guestsPanel = createStatPanel("Registered Guests",
                String.valueOf(totalGuests),
                new Color(205, 133, 63)); // Peru (brownish)

        // Available rooms panel
        int[] roomStats = dbManager.getRoomStatistics();
        int availableRooms = roomStats[2]; // available rooms
        JPanel availablePanel = createStatPanel("Available Rooms",
                String.valueOf(availableRooms),
                new Color(178, 34, 34)); // Firebrick (reddish)

        // Add stat panels to dashboard
        dashboardPanel.add(occupancyPanel);
        dashboardPanel.add(revenuePanel);
        dashboardPanel.add(guestsPanel);
        dashboardPanel.add(availablePanel);

        return dashboardPanel;
    }

    /**
     * Update statistics from database
     */
    private void updateStatistics() {
        int[] roomStats = dbManager.getRoomStatistics();
        totalRooms = roomStats[0];
        occupiedRooms = roomStats[1];
    }

    /**
     * Creates a statistics panel with title and value
     *
     * @param title The statistic title
     * @param value The statistic value
     * @param color The panel color
     * @return The statistics panel
     */
    private JPanel createStatPanel(String title, String value, Color color) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(color);
        panel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));

        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 16));
        titleLabel.setForeground(Color.WHITE);

        JLabel valueLabel = new JLabel(value);
        valueLabel.setFont(new Font("Arial", Font.BOLD, 24));
        valueLabel.setForeground(Color.WHITE);
        valueLabel.setHorizontalAlignment(JLabel.RIGHT);

        panel.add(titleLabel, BorderLayout.NORTH);
        panel.add(valueLabel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * Creates a panel with quick access buttons
     *
     * @return The quick access panel
     */
    private JPanel createQuickAccessPanel() {
        JPanel quickAccessPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 10, 10));
        quickAccessPanel.setBackground(new Color(240, 240, 245));
        quickAccessPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "Quick Access"));

        // Create quick access buttons
        String[] buttonLabels = {"New Booking", "Room Status", "Guest Search", "Housekeeping"};

        for (String label : buttonLabels) {
            JButton button = new JButton(label);
            button.setFont(new Font("Arial", Font.PLAIN, 14));
            button.setFocusPainted(false);

            button.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    welcomeLabel.setText("Quick access: " + label);
                }
            });

            quickAccessPanel.add(button);
        }

        // Add Guest Registration button
        JButton registerGuestButton = new JButton("Guest Registration");
        registerGuestButton.setFont(new Font("Arial", Font.BOLD, 14));
        registerGuestButton.setBackground(new Color(70, 130, 180)); // Steel Blue
        registerGuestButton.setForeground(Color.WHITE);
        registerGuestButton.setFocusPainted(false);
        registerGuestButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // Open registration page
                SwingUtilities.invokeLater(() -> {
                    new RegisterPage().setVisible(true);
                });
                welcomeLabel.setText("Opening Guest Registration System...");
            }
        });

        quickAccessPanel.add(registerGuestButton);

        return quickAccessPanel;
    }

    /**
     * Creates and configures the status panel
     */
    private void createStatusPanel() {
        statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBackground(new Color(240, 240, 245));
        statusPanel.setBorder(BorderFactory.createEmptyBorder(5, 15, 5, 15));
        statusPanel.setPreferredSize(new Dimension(1000, 30));

        JLabel statusLabel = new JLabel("Luxury Hotel Management System v1.0");
        statusLabel.setFont(new Font("Arial", Font.PLAIN, 12));

        statusPanel.add(statusLabel, BorderLayout.WEST);
    }

    /**
     * Sets up a timer to update the date and time label
     */
    private void setupDateTimeTimer() {
        // Update date/time every second
        dateTimeTimer = new Timer(1000, new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                updateDateTime();
            }
        });
        dateTimeTimer.start();
    }

    /**
     * Updates the date and time label with current date and time
     */
    private void updateDateTime() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEEE, MMMM d, yyyy  hh:mm:ss a");
        String dateTime = dateFormat.format(new Date());
        dateTimeLabel.setText(dateTime);
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(NewJFrame.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(NewJFrame.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(NewJFrame.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(NewJFrame.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new NewJFrame().setVisible(true);
            }
        });
    }

    // Variables declaration - do not modify//GEN-BEGIN:variables
    // End of variables declaration//GEN-END:variables
}
