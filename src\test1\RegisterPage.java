package test1;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class RegisterPage extends J<PERSON>rame {
    private JTextField usernameField;
    private JTextField firstNameField;
    private JTextField lastNameField;
    private JTextField icField;
    private JButton registerButton;
    private JButton viewUsersButton;
    private JButton backToHotelButton;

    // Database manager instance
    private DatabaseManager dbManager;

    public RegisterPage() {
        // Initialize database manager
        dbManager = DatabaseManager.getInstance();

        setTitle("Hotel Registration System");
        setSize(500, 400);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        // Create main panel with BorderLayout
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        // Create header
        JLabel headerLabel = new JLabel("Hotel Guest Registration", JLabel.CENTER);
        headerLabel.setFont(new Font("Arial", Font.BOLD, 18));
        headerLabel.setForeground(new Color(25, 25, 112)); // Midnight Blue

        // Create form panel
        JPanel formPanel = new JPanel(new GridLayout(4, 2, 10, 10));
        formPanel.setBorder(BorderFactory.createTitledBorder("Guest Information"));

        formPanel.add(new JLabel("Username:"));
        usernameField = new JTextField();
        formPanel.add(usernameField);

        formPanel.add(new JLabel("First Name:"));
        firstNameField = new JTextField();
        formPanel.add(firstNameField);

        formPanel.add(new JLabel("Last Name:"));
        lastNameField = new JTextField();
        formPanel.add(lastNameField);

        formPanel.add(new JLabel("IC Number:"));
        icField = new JTextField();
        formPanel.add(icField);

        // Create button panel
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 10));

        registerButton = new JButton("Register Guest");
        registerButton.setFont(new Font("Arial", Font.BOLD, 14));
        registerButton.setBackground(new Color(70, 130, 180)); // Steel Blue
        registerButton.setForeground(Color.WHITE);
        registerButton.setFocusPainted(false);
        registerButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                performRegistration();
            }
        });

        viewUsersButton = new JButton("View All Guests");
        viewUsersButton.setFont(new Font("Arial", Font.PLAIN, 14));
        viewUsersButton.setBackground(new Color(46, 139, 87)); // Sea Green
        viewUsersButton.setForeground(Color.WHITE);
        viewUsersButton.setFocusPainted(false);
        viewUsersButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                showAllUsers();
            }
        });

        backToHotelButton = new JButton("Back to Hotel System");
        backToHotelButton.setFont(new Font("Arial", Font.PLAIN, 14));
        backToHotelButton.setBackground(new Color(178, 34, 34)); // Firebrick
        backToHotelButton.setForeground(Color.WHITE);
        backToHotelButton.setFocusPainted(false);
        backToHotelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // Close this window and open hotel management system
                dispose();
                SwingUtilities.invokeLater(() -> {
                    new NewJFrame().setVisible(true);
                });
            }
        });

        buttonPanel.add(registerButton);
        buttonPanel.add(viewUsersButton);
        buttonPanel.add(backToHotelButton);

        // Add components to main panel
        mainPanel.add(headerLabel, BorderLayout.NORTH);
        mainPanel.add(formPanel, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        add(mainPanel);
    }

    private void performRegistration() {
        String username = usernameField.getText().trim();
        String firstName = firstNameField.getText().trim();
        String lastName = lastNameField.getText().trim();
        String ic = icField.getText().trim();

        // Validate input fields
        if (username.isEmpty() || firstName.isEmpty() || lastName.isEmpty() || ic.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "Please fill in all fields!",
                "Validation Error",
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Validate IC number format (basic validation)
        if (ic.length() < 8) {
            JOptionPane.showMessageDialog(this,
                "IC Number must be at least 8 characters long!",
                "Validation Error",
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        // Try to register user in database
        boolean success = dbManager.registerUser(username, firstName, lastName, ic);

        if (success) {
            JOptionPane.showMessageDialog(this,
                "Registration successful!\n\n" +
                "Username: " + username + "\n" +
                "Name: " + firstName + " " + lastName + "\n" +
                "IC: " + ic + "\n\n" +
                "Guest has been added to the hotel database.",
                "Registration Success",
                JOptionPane.INFORMATION_MESSAGE);

            // Clear form fields
            clearForm();
        } else {
            JOptionPane.showMessageDialog(this,
                "Registration failed!\n\n" +
                "This username or IC number may already exist in the database.\n" +
                "Please try with different details.",
                "Registration Error",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * Clear all form fields
     */
    private void clearForm() {
        usernameField.setText("");
        firstNameField.setText("");
        lastNameField.setText("");
        icField.setText("");
        usernameField.requestFocus();
    }

    /**
     * Show all registered users in a dialog
     */
    private void showAllUsers() {
        java.util.List<String[]> users = dbManager.getAllUsers();

        if (users.isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "No guests registered yet!",
                "Guest List",
                JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        // Create table data
        String[] columnNames = {"Username", "First Name", "Last Name", "IC Number", "Registration Date"};
        String[][] data = new String[users.size()][5];

        for (int i = 0; i < users.size(); i++) {
            data[i] = users.get(i);
        }

        // Create table
        JTable table = new JTable(data, columnNames);
        table.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);
        table.setRowHeight(25);
        table.getTableHeader().setFont(new Font("Arial", Font.BOLD, 12));

        // Create scroll pane
        JScrollPane scrollPane = new JScrollPane(table);
        scrollPane.setPreferredSize(new Dimension(700, 300));

        // Show in dialog
        JOptionPane.showMessageDialog(this,
            scrollPane,
            "Registered Guests (" + users.size() + " total)",
            JOptionPane.INFORMATION_MESSAGE);
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new RegisterPage().setVisible(true);
        });
    }
}
