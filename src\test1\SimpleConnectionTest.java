package test1;

import java.sql.*;

/**
 * Simple test to check if Derby server is running and accessible
 */
public class SimpleConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("=== Simple Derby Connection Test ===");
        
        // Test if Derby server is running
        testDerbyServer();
    }
    
    private static void testDerbyServer() {
        String[] urls = {
            "*************************************",
            "*****************************************"
        };
        
        for (String url : urls) {
            System.out.println("\nTesting URL: " + url);
            
            try {
                Connection conn = DriverManager.getConnection(url);
                System.out.println("✓ SUCCESS! Connected to: " + url);
                
                // Test if EMPLOYEES table exists
                DatabaseMetaData metaData = conn.getMetaData();
                ResultSet tables = metaData.getTables(null, "APP", "EMPLOYEES", null);
                
                if (tables.next()) {
                    System.out.println("✓ EMPLOYEES table found!");
                    
                    // Count records
                    Statement stmt = conn.createStatement();
                    ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as total FROM EMPLOYEES");
                    if (rs.next()) {
                        System.out.println("✓ Current records: " + rs.getInt("total"));
                    }
                    rs.close();
                    stmt.close();
                } else {
                    System.out.println("✗ EMPLOYEES table not found");
                }
                
                tables.close();
                conn.close();
                return; // Success, no need to try other URLs
                
            } catch (SQLException e) {
                System.out.println("✗ FAILED: " + e.getMessage());
                System.out.println("  SQL State: " + e.getSQLState());
                System.out.println("  Error Code: " + e.getErrorCode());
            }
        }
        
        System.out.println("\n=== All connection attempts failed ===");
        System.out.println("Possible issues:");
        System.out.println("1. Derby server is not running");
        System.out.println("2. Database 'PayrollDB' doesn't exist");
        System.out.println("3. Derby client driver not in classpath");
        System.out.println("\nTo start Derby server in NetBeans:");
        System.out.println("1. Go to Services tab");
        System.out.println("2. Right-click on Java DB");
        System.out.println("3. Select 'Start Server'");
    }
}
