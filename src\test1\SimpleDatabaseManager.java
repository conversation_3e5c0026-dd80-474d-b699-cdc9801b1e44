/*
 * Simple Database Manager using existing NetBeans Derby connection
 */
package test1;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Simple Database Manager that uses NetBeans Derby connection
 * 
 * <AUTHOR>
 */
public class SimpleDatabaseManager {
    
    // Use your existing database connection
    private static final String DB_URL = "*************************************";
    private static final String USERNAME = ""; // Usually empty for Derby
    private static final String PASSWORD = ""; // Usually empty for Derby
    
    // Singleton instance
    private static SimpleDatabaseManager instance;
    private Connection connection;
    
    /**
     * Private constructor for singleton pattern
     */
    private SimpleDatabaseManager() {
        try {
            // Try to establish connection using DriverManager
            System.out.println("Connecting to your existing Derby database...");
            connection = DriverManager.getConnection(DB_URL, USERNAME, PASSWORD);
            System.out.println("✓ Connected to PayrollDB successfully!");
            
            // Create hotel tables if they don't exist
            createHotelTables();
            
        } catch (SQLException e) {
            System.err.println("Database connection error: " + e.getMessage());
            System.err.println("Make sure Derby server is running and PayrollDB exists.");
        }
    }
    
    /**
     * Get singleton instance
     */
    public static synchronized SimpleDatabaseManager getInstance() {
        if (instance == null) {
            instance = new SimpleDatabaseManager();
        }
        return instance;
    }
    
    /**
     * Get database connection
     */
    public Connection getConnection() {
        try {
            if (connection == null || connection.isClosed()) {
                connection = DriverManager.getConnection(DB_URL, USERNAME, PASSWORD);
            }
        } catch (SQLException e) {
            System.err.println("Error getting connection: " + e.getMessage());
        }
        return connection;
    }
    
    /**
     * Create hotel tables in your existing database
     */
    private void createHotelTables() {
        try {
            Statement stmt = connection.createStatement();
            
            // Create Users table for hotel guests
            String createUsersTable = "CREATE TABLE HOTEL_USERS (" +
                "id INTEGER NOT NULL GENERATED ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1), " +
                "username VARCHAR(50) NOT NULL, " +
                "firstName VARCHAR(50) NOT NULL, " +
                "lastName VARCHAR(50) NOT NULL, " +
                "icNumber VARCHAR(20) NOT NULL, " +
                "registrationDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "PRIMARY KEY (id)" +
                ")";
            
            // Create Rooms table
            String createRoomsTable = "CREATE TABLE HOTEL_ROOMS (" +
                "roomNumber INTEGER NOT NULL, " +
                "roomType VARCHAR(20) NOT NULL, " +
                "price DECIMAL(10,2) NOT NULL, " +
                "status VARCHAR(20) DEFAULT 'AVAILABLE', " +
                "description VARCHAR(200), " +
                "PRIMARY KEY (roomNumber)" +
                ")";
            
            // Try to create tables
            try {
                stmt.executeUpdate(createUsersTable);
                System.out.println("✓ HOTEL_USERS table created successfully!");
            } catch (SQLException e) {
                if (e.getSQLState().equals("X0Y32")) {
                    System.out.println("HOTEL_USERS table already exists.");
                } else {
                    System.err.println("Error creating HOTEL_USERS table: " + e.getMessage());
                }
            }
            
            try {
                stmt.executeUpdate(createRoomsTable);
                System.out.println("✓ HOTEL_ROOMS table created successfully!");
                insertSampleRooms();
            } catch (SQLException e) {
                if (e.getSQLState().equals("X0Y32")) {
                    System.out.println("HOTEL_ROOMS table already exists.");
                } else {
                    System.err.println("Error creating HOTEL_ROOMS table: " + e.getMessage());
                }
            }
            
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error creating hotel tables: " + e.getMessage());
        }
    }
    
    /**
     * Insert sample room data
     */
    private void insertSampleRooms() {
        String insertRoom = "INSERT INTO HOTEL_ROOMS (roomNumber, roomType, price, status, description) VALUES (?, ?, ?, ?, ?)";
        
        try {
            PreparedStatement pstmt = connection.prepareStatement(insertRoom);
            
            // Sample room data
            Object[][] rooms = {
                {101, "SINGLE", 80.00, "AVAILABLE", "Standard single room"},
                {102, "SINGLE", 80.00, "OCCUPIED", "Standard single room"},
                {201, "DOUBLE", 120.00, "AVAILABLE", "Double room"},
                {202, "DOUBLE", 120.00, "AVAILABLE", "Double room"},
                {301, "SUITE", 250.00, "AVAILABLE", "Luxury suite"},
                {302, "SUITE", 250.00, "MAINTENANCE", "Luxury suite"}
            };
            
            for (Object[] room : rooms) {
                pstmt.setInt(1, (Integer) room[0]);
                pstmt.setString(2, (String) room[1]);
                pstmt.setDouble(3, (Double) room[2]);
                pstmt.setString(4, (String) room[3]);
                pstmt.setString(5, (String) room[4]);
                
                try {
                    pstmt.executeUpdate();
                } catch (SQLException e) {
                    // Ignore duplicate key errors
                    if (!e.getSQLState().equals("23505")) {
                        System.err.println("Error inserting room " + room[0] + ": " + e.getMessage());
                    }
                }
            }
            
            pstmt.close();
            System.out.println("✓ Sample rooms inserted successfully!");
            
        } catch (SQLException e) {
            System.err.println("Error inserting sample rooms: " + e.getMessage());
        }
    }
    
    /**
     * Register a new user
     */
    public boolean registerUser(String username, String firstName, String lastName, String icNumber) {
        String sql = "INSERT INTO HOTEL_USERS (username, firstName, lastName, icNumber) VALUES (?, ?, ?, ?)";
        
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, firstName);
            pstmt.setString(3, lastName);
            pstmt.setString(4, icNumber);
            
            int rowsAffected = pstmt.executeUpdate();
            pstmt.close();
            
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("Error registering user: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get all users
     */
    public List<String[]> getAllUsers() {
        List<String[]> users = new ArrayList<>();
        String sql = "SELECT username, firstName, lastName, icNumber, registrationDate FROM HOTEL_USERS ORDER BY registrationDate DESC";
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                String[] user = {
                    rs.getString("username"),
                    rs.getString("firstName"),
                    rs.getString("lastName"),
                    rs.getString("icNumber"),
                    rs.getTimestamp("registrationDate").toString()
                };
                users.add(user);
            }
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error getting users: " + e.getMessage());
        }
        
        return users;
    }
    
    /**
     * Get room statistics
     */
    public int[] getRoomStatistics() {
        int[] stats = new int[4]; // total, occupied, available, maintenance
        String sql = "SELECT status, COUNT(*) as count FROM HOTEL_ROOMS GROUP BY status";
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                String status = rs.getString("status");
                int count = rs.getInt("count");
                
                switch (status.toUpperCase()) {
                    case "OCCUPIED":
                        stats[1] = count;
                        break;
                    case "AVAILABLE":
                        stats[2] = count;
                        break;
                    case "MAINTENANCE":
                        stats[3] = count;
                        break;
                }
            }
            
            stats[0] = stats[1] + stats[2] + stats[3]; // total
            
            rs.close();
            stmt.close();
            
        } catch (SQLException e) {
            System.err.println("Error getting room statistics: " + e.getMessage());
            // Return default values
            stats[0] = 6; stats[1] = 1; stats[2] = 4; stats[3] = 1;
        }
        
        return stats;
    }
    
    /**
     * Get total guests
     */
    public int getTotalGuests() {
        String sql = "SELECT COUNT(*) as total FROM HOTEL_USERS";
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            if (rs.next()) {
                int total = rs.getInt("total");
                rs.close();
                stmt.close();
                return total;
            }
            
        } catch (SQLException e) {
            System.err.println("Error getting guest count: " + e.getMessage());
        }
        
        return 0;
    }
}
