/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package test1;

/**
 *
 * <AUTHOR>
 */
public class Test1 {

    /**
     * @param args the command line arguments
     */
    public static void main(String[] args) {
        // Check if any command line arguments are provided
        if (args.length > 0) {
            String option = args[0].toLowerCase();

            switch (option) {
                case "register":
                case "registration":
                    // Launch Registration Page
                    javax.swing.SwingUtilities.invokeLater(new Runnable() {
                        @Override
                        public void run() {
                            new RegisterPage().setVisible(true);
                        }
                    });
                    break;

                case "test":
                case "dbtest":
                    // Run database test
                    DatabaseTest.main(new String[0]);
                    break;

                default:
                    // Launch Hotel Management System (default)
                    javax.swing.SwingUtilities.invokeLater(new Runnable() {
                        @Override
                        public void run() {
                            new NewJFrame().setVisible(true);
                        }
                    });
                    break;
            }
        } else {
            // No arguments - show selection dialog
            showSelectionDialog();
        }
    }

    /**
     * Show a dialog to select which application to launch
     */
    private static void showSelectionDialog() {
        String[] options = {
            "Hotel Management System",
            "Guest Registration",
            "Database Test",
            "Exit"
        };

        int choice = javax.swing.JOptionPane.showOptionDialog(
            null,
            "Welcome to Luxury Hotel Management System!\n\nPlease select an option:",
            "Hotel Management System Launcher",
            javax.swing.JOptionPane.DEFAULT_OPTION,
            javax.swing.JOptionPane.QUESTION_MESSAGE,
            null,
            options,
            options[0]
        );

        switch (choice) {
            case 0: // Hotel Management System
                javax.swing.SwingUtilities.invokeLater(new Runnable() {
                    @Override
                    public void run() {
                        new NewJFrame().setVisible(true);
                    }
                });
                break;

            case 1: // Guest Registration
                javax.swing.SwingUtilities.invokeLater(new Runnable() {
                    @Override
                    public void run() {
                        new RegisterPage().setVisible(true);
                    }
                });
                break;

            case 2: // Database Test
                DatabaseTest.main(new String[0]);
                break;

            case 3: // Exit
            default:
                System.exit(0);
                break;
        }
    }

}
